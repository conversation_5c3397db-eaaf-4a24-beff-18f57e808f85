import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import fs from 'fs' // 新增文件系统模块
import createVitePlugins from './vite/plugins'

export default defineConfig(({ mode, command }) => {
  // 新增：动态替换favicon逻辑
  if (['bjjm', 'shyc','shxc'].includes(mode)) {
    const clientName = mode
    const sourceFavicon = path.resolve(__dirname, `client-assets/${clientName}/favicon.ico`)
    const destFavicon = path.resolve(__dirname, 'public/favicon.ico')

    // 清理旧文件
    if (fs.existsSync(destFavicon)) fs.unlinkSync(destFavicon)

    // 复制新文件
    if (fs.existsSync(sourceFavicon)) {
      fs.copyFileSync(sourceFavicon, destFavicon)
      console.log(`✅ 已注入 ${clientName} 专属 favicon`)
    } else {
      console.warn(`⚠️ 未找到 ${clientName} 的 favicon，使用默认图标`)
    }
  }

  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  return {
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    plugins: createVitePlugins(env, command === 'build'),

    // 构建配置
    build: command === 'build' ? {
      chunkSizeWarningLimit: 1000,
      // 根据不同模式设置不同的输出目录
      outDir: mode === 'shyc' ? 'dist-shyc' :
              mode === 'shxc' ? 'dist-shxc' :
              mode === 'bjjm' ? 'dist-bjjm' :
              mode === 'production' ? 'distDemo' : 'distDemo'
    } : {},
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src')
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    server: {
      port: 8089,
      host: true,
      open: true,
      proxy: {
        '/dev-api': {
          target: 'http://localhost:18080',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          }
        ]
      }
    }
  }
})
