# Word文档解析打印功能

## 安装依赖

为了支持在浏览器中直接解析Word文档并打印，需要安装以下依赖：

```bash
# 安装mammoth库用于解析Word文档
npm install mammoth

# 可选：安装docx-preview作为备用方案
npm install docx-preview
```

## 功能说明

### 方案1：使用mammoth解析Word（推荐）

**优势**：
- ✅ 直接在浏览器中解析Word文档
- ✅ 转换为HTML格式显示
- ✅ 支持表格、样式、图片等
- ✅ 可以直接使用浏览器打印功能
- ✅ 无需下载文件

**使用流程**：
1. 点击"打印工单"按钮
2. 系统获取Word文档数据
3. 使用mammoth解析为HTML
4. 在新窗口中显示内容
5. 点击"打印"按钮或使用Ctrl+P快捷键

### 方案2：备用下载方案

如果mammoth库未安装或解析失败，会自动回退到下载方案：
- 在新窗口中提供下载链接
- 用户下载后使用Word软件打印

## 技术实现

### 核心代码逻辑：

```javascript
// 1. 获取Word文档数据
const response = await orderFlowExport(uuid);

// 2. 解析Word文档
const mammoth = await import('mammoth');
const arrayBuffer = await wordBlob.arrayBuffer();
const result = await mammoth.convertToHtml({ arrayBuffer });

// 3. 在新窗口中显示并打印
const printWindow = window.open('', '_blank');
printWindow.document.write(htmlContent);
```

### 支持的功能：

- **自动解析**：自动将Word文档转换为HTML
- **样式保持**：保持原有的表格、字体、颜色等样式
- **快捷键支持**：Ctrl+P打印，ESC关闭窗口
- **响应式设计**：适配不同屏幕尺寸
- **打印优化**：专门的打印样式，隐藏不必要元素

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 注意事项

1. **依赖安装**：首次使用需要安装mammoth库
2. **文件大小**：大型Word文档可能解析较慢
3. **复杂格式**：某些复杂的Word格式可能无法完美转换
4. **图片支持**：嵌入的图片会被转换为base64格式

## 错误处理

- 如果mammoth库未安装，自动回退到下载方案
- 如果解析失败，显示错误信息并提供下载选项
- 如果无法打开新窗口，提示用户检查浏览器设置

## 性能优化

- 使用动态导入减少初始包大小
- 异步处理避免阻塞UI
- 自动清理资源防止内存泄漏
