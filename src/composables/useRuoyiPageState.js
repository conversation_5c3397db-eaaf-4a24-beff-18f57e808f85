import { ref, onMounted, onActivated, onDeactivated, nextTick, getCurrentInstance, readonly } from 'vue'
import { useRoute } from 'vue-router'
import useTagsViewStore from '@/store/modules/tagsView'

/**
 * 若依Vue3脚手架专用的页面状态管理
 * 针对若依的特点进行优化
 */
export function useRuoyiPageState(options = {}) {
  const {
    // 是否启用调试日志
    debug = false,
    // 初始化函数
    initFunction = null,
    // 激活时的回调函数
    onActivatedCallback = null,
    // 失活时的回调函数
    onDeactivatedCallback = null,
    // 是否在每次激活时都重新初始化
    reinitOnActivated = false,
    // 是否自动管理loading状态
    autoLoading = true,
    // 是否自动处理权限检查
    autoPermissionCheck = true
  } = options

  const route = useRoute()
  const { proxy } = getCurrentInstance()
  const tagsViewStore = useTagsViewStore()
  
  // 页面状态
  const pageState = ref({
    mounted: false,
    activated: false,
    initialized: false,
    loading: false,
    error: null,
    lastActivated: null,
    activationCount: 0,
    permissions: [],
    hasPermission: true
  })

  // 调试日志函数
  function log(...args) {
    if (debug && import.meta.env.DEV) {
      console.log(`[RuoyiPageState:${route.name || route.path}]`, ...args)
    }
  }

  // 检查页面权限
  function checkPermissions() {
    if (!autoPermissionCheck) return true
    
    const routePermissions = route.meta?.permissions || []
    if (routePermissions.length === 0) return true
    
    // 使用若依的权限检查方法
    if (proxy && proxy.$auth) {
      const hasPermission = proxy.$auth.hasPermi(routePermissions)
      pageState.value.hasPermission = hasPermission
      pageState.value.permissions = routePermissions
      
      if (!hasPermission) {
        log('Permission check failed:', routePermissions)
        return false
      }
    }
    
    return true
  }

  // 初始化页面
  async function initializePage() {
    if (pageState.value.loading) {
      log('Already initializing, skipping...')
      return
    }

    try {
      if (autoLoading) {
        pageState.value.loading = true
      }
      pageState.value.error = null
      log('Initializing page...')

      // 检查权限
      if (!checkPermissions()) {
        throw new Error('权限不足')
      }

      // 确保页面在TagsView中正确注册
      if (route.name && !tagsViewStore.cachedViews.includes(route.name)) {
        log('Adding page to cache:', route.name)
        tagsViewStore.addView(route)
      }

      if (initFunction && typeof initFunction === 'function') {
        await initFunction()
      }

      pageState.value.initialized = true
      log('Page initialized successfully')
    } catch (error) {
      pageState.value.error = error
      log('Page initialization failed:', error)
      
      // 若依风格的错误提示
      if (proxy && proxy.$modal) {
        if (error.message === '权限不足') {
          proxy.$modal.msgError('您没有访问此页面的权限')
        } else {
          proxy.$modal.msgError('页面初始化失败: ' + (error.message || '未知错误'))
        }
      }
      
      throw error
    } finally {
      if (autoLoading) {
        pageState.value.loading = false
      }
    }
  }

  // 重置页面状态
  function resetPageState() {
    log('Resetting page state')
    pageState.value = {
      mounted: false,
      activated: false,
      initialized: false,
      loading: false,
      error: null,
      lastActivated: null,
      activationCount: 0,
      permissions: [],
      hasPermission: true
    }
  }

  // 检查页面是否需要重新初始化
  function shouldReinitialize() {
    if (reinitOnActivated) return true
    if (!pageState.value.initialized) return true
    if (pageState.value.error) return true
    
    // 检查路由参数是否变化
    if (route.params && Object.keys(route.params).length > 0) {
      const currentParams = JSON.stringify(route.params)
      const lastParams = pageState.value.lastParams
      if (lastParams && lastParams !== currentParams) {
        log('Route params changed, reinitializing')
        return true
      }
      pageState.value.lastParams = currentParams
    }
    
    return false
  }

  // 处理页面激活
  async function handleActivation() {
    pageState.value.activated = true
    pageState.value.lastActivated = Date.now()
    pageState.value.activationCount++
    
    log('Page activated', {
      activationCount: pageState.value.activationCount,
      initialized: pageState.value.initialized,
      shouldReinit: shouldReinitialize(),
      routeName: route.name,
      routePath: route.path
    })

    // 等待下一个tick确保DOM完全更新
    await nextTick()

    try {
      if (shouldReinitialize()) {
        await initializePage()
      }

      if (onActivatedCallback && typeof onActivatedCallback === 'function') {
        await onActivatedCallback()
      }
    } catch (error) {
      log('Activation handling failed:', error)
      // 不抛出错误，避免影响页面渲染
    }
  }

  // 处理页面失活
  function handleDeactivation() {
    pageState.value.activated = false
    log('Page deactivated')

    if (onDeactivatedCallback && typeof onDeactivatedCallback === 'function') {
      try {
        onDeactivatedCallback()
      } catch (error) {
        log('Deactivation callback failed:', error)
      }
    }
  }

  // 生命周期钩子
  onMounted(async () => {
    pageState.value.mounted = true
    log('Page mounted')

    try {
      await initializePage()
    } catch (error) {
      log('Mount initialization failed:', error)
    }
  })

  onActivated(async () => {
    await handleActivation()
  })

  onDeactivated(() => {
    handleDeactivation()
  })

  // 手动刷新页面
  async function refreshPage() {
    log('Manually refreshing page')
    pageState.value.initialized = false
    await initializePage()
  }

  // 若依风格的操作方法
  const ruoyiMethods = {
    // 显示成功消息
    msgSuccess: (message) => {
      if (proxy && proxy.$modal) {
        proxy.$modal.msgSuccess(message)
      }
    },
    
    // 显示错误消息
    msgError: (message) => {
      if (proxy && proxy.$modal) {
        proxy.$modal.msgError(message)
      }
    },
    
    // 显示确认对话框
    confirm: (message) => {
      if (proxy && proxy.$modal) {
        return proxy.$modal.confirm(message)
      }
      return Promise.resolve()
    },
    
    // 检查权限
    hasPermission: (permissions) => {
      if (proxy && proxy.$auth) {
        return proxy.$auth.hasPermi(permissions)
      }
      return true
    },
    
    // 获取字典数据
    getDictData: (dictType) => {
      if (proxy && proxy.getDictData) {
        return proxy.getDictData(dictType)
      }
      return []
    }
  }

  // 返回状态和方法
  return {
    pageState: readonly(pageState),
    initializePage,
    resetPageState,
    refreshPage,
    checkPermissions,
    log,
    ...ruoyiMethods
  }
}

/**
 * 若依列表页面专用状态管理
 */
export function useRuoyiListPageState(getListFunction, options = {}) {
  const {
    // 是否自动处理分页
    autoPagination = true,
    // 默认页面大小
    defaultPageSize = 10,
    // 是否自动处理搜索
    autoSearch = true,
    ...otherOptions
  } = options

  const listState = ref({
    data: [],
    loading: false,
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    queryParams: {},
    searchForm: {}
  })

  const initFunction = async () => {
    if (getListFunction && typeof getListFunction === 'function') {
      listState.value.loading = true
      try {
        await getListFunction()
      } finally {
        listState.value.loading = false
      }
    }
  }

  const pageStateComposable = useRuoyiPageState({
    ...otherOptions,
    initFunction,
    autoLoading: false // 由listState管理loading
  })

  // 若依列表页面的常用方法
  const listMethods = {
    // 处理分页变化
    handlePageChange: (page) => {
      listState.value.currentPage = page
      if (getListFunction) {
        getListFunction()
      }
    },
    
    // 处理页面大小变化
    handleSizeChange: (size) => {
      listState.value.pageSize = size
      listState.value.currentPage = 1
      if (getListFunction) {
        getListFunction()
      }
    },
    
    // 搜索
    handleSearch: () => {
      listState.value.currentPage = 1
      if (getListFunction) {
        getListFunction()
      }
    },
    
    // 重置搜索
    resetSearch: () => {
      listState.value.searchForm = {}
      listState.value.queryParams = {}
      listState.value.currentPage = 1
      if (getListFunction) {
        getListFunction()
      }
    },
    
    // 设置列表数据
    setListData: (data, total = 0) => {
      listState.value.data = data
      listState.value.total = total
    },
    
    // 设置加载状态
    setLoading: (loading) => {
      listState.value.loading = loading
    }
  }

  return {
    ...pageStateComposable,
    listState: readonly(listState),
    ...listMethods
  }
}
