import { ref, onMounted, onActivated, onDeactivated, nextTick, getCurrentInstance, readonly } from 'vue'
import { useRoute } from 'vue-router'

/**
 * 页面状态管理 composable
 * 用于解决页签切换时内容空白的问题
 */
export function usePageState(options = {}) {
  const {
    // 是否启用调试日志
    debug = false,
    // 初始化函数
    initFunction = null,
    // 激活时的回调函数
    onActivatedCallback = null,
    // 失活时的回调函数
    onDeactivatedCallback = null,
    // 是否在每次激活时都重新初始化
    reinitOnActivated = false
  } = options

  const route = useRoute()
  const { proxy } = getCurrentInstance()
  
  // 页面状态
  const pageState = ref({
    mounted: false,
    activated: false,
    initialized: false,
    loading: false,
    error: null,
    lastActivated: null,
    activationCount: 0
  })

  // 调试日志函数
  function log(...args) {
    if (debug) {
      console.log(`[PageState:${route.path}]`, ...args)
    }
  }

  // 初始化页面
  async function initializePage() {
    if (pageState.value.loading) {
      log('Already initializing, skipping...')
      return
    }

    try {
      pageState.value.loading = true
      pageState.value.error = null
      log('Initializing page...')

      if (initFunction && typeof initFunction === 'function') {
        await initFunction()
      }

      pageState.value.initialized = true
      log('Page initialized successfully')
    } catch (error) {
      pageState.value.error = error
      log('Page initialization failed:', error)
      throw error
    } finally {
      pageState.value.loading = false
    }
  }

  // 重置页面状态
  function resetPageState() {
    log('Resetting page state')
    pageState.value = {
      mounted: false,
      activated: false,
      initialized: false,
      loading: false,
      error: null,
      lastActivated: null,
      activationCount: 0
    }
  }

  // 检查页面是否需要重新初始化
  function shouldReinitialize() {
    if (reinitOnActivated) return true
    if (!pageState.value.initialized) return true
    if (pageState.value.error) return true
    return false
  }

  // 处理页面激活
  async function handleActivation() {
    pageState.value.activated = true
    pageState.value.lastActivated = Date.now()
    pageState.value.activationCount++
    
    log('Page activated', {
      activationCount: pageState.value.activationCount,
      initialized: pageState.value.initialized,
      shouldReinit: shouldReinitialize()
    })

    // 等待下一个tick确保DOM完全更新
    await nextTick()

    try {
      if (shouldReinitialize()) {
        await initializePage()
      }

      if (onActivatedCallback && typeof onActivatedCallback === 'function') {
        await onActivatedCallback()
      }
    } catch (error) {
      log('Activation handling failed:', error)
      // 不抛出错误，避免影响页面渲染
    }
  }

  // 处理页面失活
  function handleDeactivation() {
    pageState.value.activated = false
    log('Page deactivated')

    if (onDeactivatedCallback && typeof onDeactivatedCallback === 'function') {
      try {
        onDeactivatedCallback()
      } catch (error) {
        log('Deactivation callback failed:', error)
      }
    }
  }

  // 生命周期钩子
  onMounted(async () => {
    pageState.value.mounted = true
    log('Page mounted')

    try {
      await initializePage()
    } catch (error) {
      log('Mount initialization failed:', error)
      // 在mounted阶段失败时，可以显示错误信息
      if (proxy && proxy.$modal) {
        proxy.$modal.msgError('页面初始化失败: ' + (error.message || '未知错误'))
      }
    }
  })

  onActivated(async () => {
    await handleActivation()
  })

  onDeactivated(() => {
    handleDeactivation()
  })

  // 手动刷新页面
  async function refreshPage() {
    log('Manually refreshing page')
    pageState.value.initialized = false
    await initializePage()
  }

  // 返回状态和方法
  return {
    pageState: readonly(pageState),
    initializePage,
    resetPageState,
    refreshPage,
    log
  }
}

/**
 * 专门用于列表页面的状态管理
 */
export function useListPageState(getListFunction, options = {}) {
  const listState = ref({
    data: [],
    loading: false,
    total: 0,
    currentPage: 1,
    pageSize: 10
  })

  const initFunction = async () => {
    if (getListFunction && typeof getListFunction === 'function') {
      listState.value.loading = true
      try {
        await getListFunction()
      } finally {
        listState.value.loading = false
      }
    }
  }

  const pageStateComposable = usePageState({
    ...options,
    initFunction
  })

  return {
    ...pageStateComposable,
    listState: readonly(listState),
    setListData: (data, total = 0) => {
      listState.value.data = data
      listState.value.total = total
    },
    setLoading: (loading) => {
      listState.value.loading = loading
    }
  }
}
