<template>
  <div class="redirect-container">
    <div class="loading-wrapper">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>页面重新加载中...</span>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { nextTick, onMounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'

const route = useRoute();
const router = useRouter();
const { params, query } = route
const { path } = params

// 确保在下一个tick执行重定向，避免路由冲突
onMounted(async () => {
  try {
    // 等待DOM更新完成
    await nextTick();

    // 添加小延迟确保组件完全挂载
    setTimeout(async () => {
      try {
        await router.replace({ path: '/' + path, query });
      } catch (error) {
        // 如果重定向失败，尝试回到首页
        await router.replace('/');
      }
    }, 50);
  } catch (error) {
    // 立即尝试重定向
    router.replace({ path: '/' + path, query });
  }
});
</script>

<style scoped>
.redirect-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #409eff;
}

.loading-wrapper span {
  font-size: 14px;
}
</style>
