<template>
  <div class="app-container">
    <Splitpanes class="default-theme" :horizontal="false">
      <Pane :size="65">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable @keyup.enter="handleQuery"
              style="width: 150px;" />
          </el-form-item>
          <el-form-item label="联系人" prop="contact">
            <el-input v-model="queryParams.contact" placeholder="请输入联系人" clearable @keyup.enter="handleQuery"
              style="width: 150px;" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['business:Customer:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
              v-hasPermi="['business:Customer:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['business:Customer:remove']">删除</el-button>
          </el-col>

          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table border v-loading="loading" :data="CustomerList" @selection-change="handleSelectionChange"
          @row-click="handleRowClick" highlight-current-row :current-row-key="selectedCustomer?.uuid">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column sortable label="客户名称" align="center" prop="customerName" />
          <el-table-column sortable label="客户等级" align="center" prop="levelName">
            <template #default="scope">
              <el-popover placement="right" :width="300" trigger="hover" popper-class="discount-popover">
                <template #reference>
                  <span style="cursor: pointer; color: #409EFF;">{{ scope.row.levelName }}</span>
                </template>
                <div class="discount-info">
                  <h4 style="margin: 0 0 10px 0">工艺折扣信息</h4>
                  <el-table :data="scope.row.discountCraftList" border size="small" style="width: 100%">
                    <el-table-column label="工艺种类" align="center" width="90">
                      <template #default="scope">
                        <el-tag size="small" :type="scope.row.craftClassType === '9999' ? 'success' : 'warning'"
                          effect="plain">
                          {{scope.row.craftClassType === '9999' ? '印刷' : m207.find(dict => dict.value ===
                            scope.row.craftClassType)?.label }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="数量范围" align="center" width="110">
                      <template #default="scope">
                        <span>{{ scope.row.minQuantity }}</span>
                        <span v-if="scope.row.maxQuantity">-{{ scope.row.maxQuantity }}</span>
                        <span v-else>份以上</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="折扣率" align="center" width="70">
                      <template #default="scope">
                        <el-tag size="small" :type="getDiscountRateType(scope.row.discountRate)" effect="plain">
                          {{ (scope.row.discountRate * 100).toFixed(0) }}%
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column sortable label="地址" align="center" prop="address" />
          <el-table-column sortable label="联系人" align="center" prop="contact" />
          <el-table-column sortable label="联系电话" align="center" prop="phone" />

          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="{ row }">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(row)"
                v-hasPermi="['business:Customer:edit']">修改</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(row)"
                v-hasPermi="['business:Customer:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </Pane>
      <Pane :size="35">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="客户地址" name="address">
            <addressList ref="addressListRef" :customer-uuid="selectedCustomer?.uuid"
              :customer-name="selectedCustomer?.customerName">
            </addressList>
          </el-tab-pane>
          <el-tab-pane label="客户用户" name="user">
            <msUserList ref="msUserListRef" :customer-uuid="selectedCustomer?.uuid"
              :customer-name="selectedCustomer?.customerName">
            </msUserList>
          </el-tab-pane>
        </el-tabs>
      </Pane>
    </Splitpanes>

    <!-- 添加或修改客户信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="CustomerRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="form.customerName" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="客户等级" prop="levelUuid">
          <el-select v-model="form.levelUuid" placeholder="请选择客户等级" style="width: 100%">
            <el-option v-for="item in discountLevelOptions" :key="item.uuid" :label="item.levelName" :value="item.uuid">
              <span>{{ item.levelName }}</span>
              <!-- <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                折扣率: {{ item.discountCraftList?.[0]?.discountRate * 100 }}%
              </span> -->
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" maxlength="11" />
<!--          <div style="font-size: 12px; color: #909399; margin-top: 4px;">-->
<!--            手机号将作为商城登录账号使用-->
<!--          </div>-->
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="customer">
import { listCustomer, getCustomer, delCustomer, addCustomer, updateCustomer } from "@/api/business/customer.js";
import { getDiscountLevelAllList } from "@/api/business/discountLevel";
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import addressList from "./addressList.vue";
import msUserList from "./msUserList.vue";
import { ref, reactive, toRefs, nextTick, onMounted } from 'vue';
import { useRouter } from 'vue-router';
const { proxy } = getCurrentInstance();
const { sys_common_status, m202, m207 } = proxy.useDict('sys_common_status', 'm202', 'm207');

const router = useRouter();

const CustomerList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const selectedCustomer = ref(null);
const addressListRef = ref(null);
const customerUserListRef = ref(null);
const discountLevelOptions = ref([]);
const activeTab = ref('address');

// 获取所有客户等级
async function getDiscountLevels() {
  try {
    const response = await getDiscountLevelAllList();
    console.log(response);
    if (response.code === 200) {
      discountLevelOptions.value = response.data || [];
      if (discountLevelOptions.value.length === 0) {
        proxy.$modal.msgError("暂无可用的客户等级，请先创建客户等级");
      }
    } else {
      proxy.$modal.msgError(response.msg || "获取客户等级失败");
    }
  } catch (error) {
    console.error("获取客户等级失败", error);
    proxy.$modal.msgError("获取客户等级失败");
  }
}

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerCode: null,
    customerName: null,
    orgUuid: null,
    address: null,
    contact: null,
    phone: null,
    status: null,
    merchantUuid: null,
    customerType: null,
    levelUuid: null
  },
  rules: {
    customerName: [
      { required: true, message: "客户名称不能为空", trigger: "blur" }
    ],
    levelUuid: [
      { required: true, message: "请选择客户等级", trigger: ["change", "blur"] }
    ],
    phone: [
      { required: true, message: "手机号不能为空", trigger: "blur" },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 点击行选中客户 */
function handleRowClick(row) {
  selectedCustomer.value = row;
  // 确保地址列表和用户列表组件重新加载
  nextTick(() => {
    if (addressListRef.value) {
      addressListRef.value.loadAddressList();
    }
    if (msUserListRef.value) {
      msUserListRef.value.loadUserList();
    }
  });
}

/** 查询客户信息列表 */
function getList() {
  loading.value = true;
  listCustomer(queryParams.value).then(response => {
    CustomerList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 如果有数据且没有选中的客户，自动选中第一条
    if (CustomerList.value.length > 0 && !selectedCustomer.value) {
      selectedCustomer.value = CustomerList.value[0];
      nextTick(() => {
        if (addressListRef.value) {
          addressListRef.value.loadAddressList();
        }
      });
    }
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    uuid: null,
    customerCode: null,
    customerName: null,
    orgUuid: null,
    address: null,
    contact: null,
    phone: null,
    status: null,
    merchantUuid: null,
    createTime: null,
    createBy: null,
    updateTime: null,
    updateBy: null,
    customerType: null,
    levelUuid: null
  };
  proxy.resetForm("CustomerRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.uuid);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
async function handleAdd() {
  reset();
  await getDiscountLevels();
  // 只有在没有任何等级时才阻止打开表单
  if (discountLevelOptions.value.length === 0) {
    return;
  }
  open.value = true;
  title.value = "添加客户信息";
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  await getDiscountLevels();
  // 只有在没有任何等级时才阻止打开表单
  if (discountLevelOptions.value.length === 0) {
    return;
  }
  const _uuid = row.uuid || ids.value
  getCustomer(_uuid).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改客户信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["CustomerRef"].validate(valid => {
    if (valid) {
      if (!form.value.levelUuid) {
        proxy.$modal.msgError("请选择客户等级");
        return;
      }

      if (form.value.uuid != null) {
        updateCustomer(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCustomer(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _uuids = row.uuid || ids.value;
  proxy.$modal.confirm('是否确认删除客户信息编号为"' + _uuids + '"的数据项？').then(function () {
    return delCustomer(_uuids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('business/Customer/export', {
    ...queryParams.value
  }, `Customer_${new Date().getTime()}.xlsx`)
}

// 根据折扣率返回不同的标签类型
const getDiscountRateType = (rate) => {
  const percentage = rate * 100;
  if (percentage >= 95) return 'primary';
  if (percentage >= 90) return 'success';
  if (percentage >= 80) return 'warning';
  return 'danger';
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.app-container {
  height: calc(100vh - 84px);
}

:deep(.splitpanes) {
  background-color: #f5f7fa;
}

:deep(.splitpanes__pane) {
  background-color: white;
  padding: 10px;
  box-sizing: border-box;
}

:deep(.splitpanes__splitter) {
  background-color: #e4e7ed;
  position: relative;

  &:before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 2px;
    background-color: #909399;
    border-radius: 2px;
  }

  &:hover {
    background-color: #dcdfe6;
  }
}

.discount-info {
  padding: 0;

  :deep(.el-table) {
    margin-top: 8px;
  }
}
</style>
