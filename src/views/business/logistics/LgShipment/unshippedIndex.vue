<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" width="120px" />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable @keyup.enter="handleQuery" width="120px"/>
        </el-form-item>
        <el-form-item label="发货状态" prop="shippingStatus">
          <el-select v-model="queryParams.shippingStatus" placeholder="请选择状态" clearable style="width: 100px">
            <el-option v-for="dict in shipping_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="Van" :disabled="multiple" @click="handleShipment">创建发货单</el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      ref="tableRef"
      border
      v-loading="loading"
      :data="unshippedOrderList"
      @selection-change="handleSelectionChange"
      :row-class-name="tableRowClassName">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="orderNo" sortable width="150">
        <template #default="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.orderNo }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priority" sortable>
        <template #default="scope">
          <dict-tag type="info" :options="m209" :value="scope.row.priority"/>
        </template>
      </el-table-column>
      <el-table-column label="客户" align="center" prop="customerName" sortable />
      <el-table-column label="联系人" align="center" prop="contact">
        <template #default="scope">
          <span>{{ scope.row.contact }} / {{ scope.row.phone }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地址" align="center" prop="address" sortable>
        <template #default="scope">
          <span :class="{ 'text-red': scope.row.isPickup === 'Y' }">
            {{ scope.row.isPickup === 'Y' ? '自提' : scope.row.address }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="发货状态" align="center" prop="shippingStatus" width="100">
        <template #default="scope">
          <dict-tag :options="shipping_status" :value="scope.row.shippingStatus" />
        </template>
      </el-table-column>
      <el-table-column label="订单份数" align="center" prop="orderNum" width="100" sortable />

      <el-table-column label="未发数量" align="center" prop="unshippedNum" width="100" sortable>
        <template #default="scope">
          <span>{{ scope.row.orderNum - scope.row.shippedNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交货时间" align="center" prop="deliveryTime" sortable width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deliveryTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="交货倒计时" align="center" width="150">
        <template #default="scope">
          <span :class="{ 'text-danger': isUrgent(scope.row.deliveryTime) }">
            {{ getCountdown(scope.row.deliveryTime) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="最近发货时间" align="center" prop="lastShipmentTime" sortable>
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastShipmentTime) }}</span>
        </template>
      </el-table-column>

    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getUnshippedOrderListPage" />

    <!-- 创建发货单对话框 -->
    <el-dialog draggable :title="'创建发货单'" v-model="shipmentOpen" width="800px" append-to-body>
      <el-form ref="shipmentFormRef" :model="shipmentForm" :rules="shipmentRules" label-width="120px">
        <el-form-item label="联系人" prop="contact">
          <el-input v-model="shipmentForm.contact" placeholder="请输入联系人" style="width: 200px" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="shipmentForm.contactPhone" placeholder="请输入联系电话" style="width: 200px" />
        </el-form-item>

        <el-form-item label="收货地址" prop="address">
          <el-input v-model="shipmentForm.address" type="textarea" placeholder="请输入收货地址" :rows="2" style="width: 400px"
            maxlength="200" show-word-limit />
        </el-form-item>
        <el-divider />
        <el-form-item label="物流类型" prop="logisticsType">
          <el-radio-group v-model="shipmentForm.logisticsType">
            <el-radio-button v-for="item in logistics_type" :key="item.value" :label="item.label" :value="item.value" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCompanyUuid" v-if="shipmentForm.logisticsType === 'third_party'">
          <el-select v-model="shipmentForm.logisticsCompanyUuid" placeholder="请选择物流公司" style="width: 200px">
            <el-option v-for="item in logisticsCompanyOptions" :key="item.uuid" :label="item.companyName"
              :value="item.uuid" />
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" prop="trackingNo" v-if="shipmentForm.logisticsType !== 'pickup'">
          <el-input v-model="shipmentForm.trackingNo" placeholder="请输入快递单号" style="width: 200px" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="shipmentForm.remark" type="textarea" placeholder="请输入备注信息" :rows="2" style="width: 400px"
            maxlength="200" show-word-limit />
        </el-form-item>

        <!-- 发货明细表格 -->
        <el-table border :data="lgShipmentDetailList" style="width: 100%">
          <el-table-column label="订单号" prop="orderNo" align="center" width="150" />
          <!-- <el-table-column label="产品名称" prop="productName" align="center" width="200"/> -->
          <el-table-column label="订单份数" prop="orderNum" align="center" width="100" />
          <!-- <el-table-column label="已发数量" prop="shippedNum" align="center" width="100" /> -->
          <el-table-column label="未发数量" prop="unshippedNum" align="center" width="100" />
          <el-table-column label="本次发货数量" align="center" width="150">
            <template #default="scope">
              <el-input-number v-model="scope.row.quantity" :min="0" :max="scope.row.unshippedNum" />
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.remark" placeholder="请输入备注" maxlength="200" show-word-limit />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitShipment">确 定</el-button>
          <el-button @click="cancelShipment">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>

      <!-- 使用新的订单详情组件 -->
      <OrderDetail v-model="detailVisible" :order-uuid="currentOrderUuid" :title="'订单详情'" />
</template>

<script setup name="unshippedIndex">
import { ref, reactive, toRefs, onMounted, watch, onBeforeUnmount, nextTick } from 'vue';
import { unshippedOrderListPage } from "@/api/order/order";
import { parseTime } from '@/utils/ruoyi';
import { listLgLogisticsCompany } from "@/api/logistics/LgLogisticsCompany";
import { listLgShipment, addLgShipment } from "@/api/logistics/LgShipment";
import OrderDetail from '@/views/order/order/components/orderDetailDrawer.vue'

const { proxy } = getCurrentInstance();

const { m209, m214, shipping_status, logistics_type } = proxy.useDict('m209', 'm214', 'shipping_status', 'logistics_type');

const unshippedOrderList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
// 添加详情相关的数据
const detailVisible = ref(false);
const currentOrderUuid = ref('');

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: undefined,
    customerName: undefined
  }
});

const { queryParams } = toRefs(data);

// 发货单相关数据
const shipmentOpen = ref(false);
const logisticsCompanyOptions = ref([]);
const lgShipmentDetailList = ref([]);

const shipmentForm = reactive({
  logisticsType: 'third_party',
  logisticsCompanyUuid: undefined,
  trackingNo: undefined,
  remark: undefined,
  orderUuids: [],
  customerUuid: undefined,
  customerName: undefined,
  contact: undefined,
  contactPhone: undefined,
  address: undefined,
  details: []
});

const shipmentRules = {
  logisticsType: [
    { required: true, message: "请选择物流类型", trigger: "change" }
  ],
  logisticsCompanyUuid: [
    { required: true, message: "请选择物流公司", trigger: "change" }
  ]
};

// 动态设置表单验证规则
const updateRules = () => {
  // 重置规则
  shipmentRules.logisticsCompanyUuid = shipmentForm.logisticsType === 'third_party'
    ? [{ required: true, message: "请选择物流公司", trigger: "change" }] : undefined;
};

/** 表格行的类名 */
const tableRowClassName = ({ row }) => {
  if (row.shippingStatus === 'shipped') {
    return 'shipped-row';
  }
  return '';
};

/** 查询待发货订单列表 */
function getUnshippedOrderListPage() {
  loading.value = true;
  unshippedOrderListPage(queryParams.value).then(response => {
    unshippedOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getUnshippedOrderListPage();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    orderNo: undefined,
    customerName: undefined
  };
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.uuid);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 获取物流公司列表 */
function getLogisticsCompanyList() {
  listLgLogisticsCompany({ pageSize: 100 }).then(response => {
    logisticsCompanyOptions.value = response.rows;
  });
}

/** 创建发货单按钮操作 */
function handleShipment() {
  if (ids.value.length === 0) {
    proxy.$modal.msgError("请选择要发货的订单");
    return;
  }

  // 根据选中的行构建发货明细
  const selectedRows = unshippedOrderList.value.filter(item => ids.value.includes(item.uuid));

  // 检查是否有已发货完成的订单
  const shippedOrders = selectedRows.filter(row => row.shippingStatus === 'shipped');
  if (shippedOrders.length > 0) {
    const orderNos = shippedOrders.map(row => row.orderNo).join('、');
    proxy.$modal.msgError(`订单${orderNos}已完成发货，不能重复发货`);
    return;
  }

  // 验证是否为相同客户
  const customerUuid = selectedRows[0].customerUuid;
  const isAllSameCustomer = selectedRows.every(row => row.customerUuid === customerUuid);

  if (!isAllSameCustomer) {
    proxy.$modal.msgError("只能选择相同客户的订单创建发货单");
    return;
  }

  // 检查联系人和地址是否相同
  const firstRow = selectedRows[0];
  const hasDifferentContact = selectedRows.some(
    row => row.contact !== firstRow.contact ||
          row.phone !== firstRow.phone ||
          row.address !== firstRow.address
  );

  if (hasDifferentContact) {
    proxy.$modal.msgWarning("选中订单的联系人或地址不同，已默认使用第一个订单的信息");
  }

  lgShipmentDetailList.value = selectedRows.map(item => ({
    ...item,
    unshippedNum: item.orderNum - item.shippedNum,
    quantity: item.orderNum - item.shippedNum,
    itemType: 'full',
    productMainUuid: item.productMainUuid,
    productName: item.productName,
    remark: ''
  }));

  // 打开弹窗
  shipmentOpen.value = true;
  // 重置表单
  shipmentForm.logisticsType = 'third_party';
  shipmentForm.logisticsCompanyUuid = undefined;
  shipmentForm.trackingNo = undefined;
  shipmentForm.remark = undefined;
  shipmentForm.orderUuids = ids.value;
  // 设置客户信息
  shipmentForm.customerUuid = customerUuid;
  shipmentForm.customerName = selectedRows[0].customerName;
  // 设置联系人信息
  shipmentForm.contact = firstRow.contact;
  shipmentForm.contactPhone = firstRow.phone;
  shipmentForm.address = firstRow.address;

  // 更新验证规则
  updateRules();
}

/** 取消发货单 */
function cancelShipment() {
  shipmentOpen.value = false;
  lgShipmentDetailList.value = [];
}

/** 提交发货单 */
function submitShipment() {
  proxy.$refs["shipmentFormRef"].validate(valid => {
    if (valid) {
      // 验证发货数量
      const invalidQuantity = lgShipmentDetailList.value.some(
        item => item.quantity <= 0 || item.quantity > item.unshippedNum
      );

      if (invalidQuantity) {
        proxy.$modal.msgError("发货数量不能为0或大于未发数量");
        return;
      }

      // 计算总发货数量
      const totalQuantity = lgShipmentDetailList.value.reduce(
        (sum, item) => sum + item.quantity, 0
      );

      // 获取选中的物流公司信息
      let logisticsCompanyName = null;
      if (shipmentForm.logisticsType === 'third_party' && shipmentForm.logisticsCompanyUuid) {
        const selectedCompany = logisticsCompanyOptions.value.find(
          item => item.uuid === shipmentForm.logisticsCompanyUuid
        );
        logisticsCompanyName = selectedCompany ? selectedCompany.companyName : null;
      }

      // 构建提交的数据
      const form = {
        logisticsType: shipmentForm.logisticsType,
        logisticsCompanyUuid: shipmentForm.logisticsType === 'third_party' ? shipmentForm.logisticsCompanyUuid : null,
        logisticsCompanyName: logisticsCompanyName,
        trackingNo: shipmentForm.logisticsType !== 'pickup' ? shipmentForm.trackingNo : null,
        totalQuantity: totalQuantity,
        remark: shipmentForm.remark,
        customerUuid: shipmentForm.customerUuid,
        customerName: shipmentForm.customerName,
        contact: shipmentForm.contact,
        contactPhone: shipmentForm.contactPhone,
        address: shipmentForm.address,
        lgShipmentDetailList: lgShipmentDetailList.value.map(item => ({
          orderUuid: item.uuid,
          orderNo: item.orderNo,
          itemType: item.itemType,
          productMainUuid: item.productMainUuid,
          productName: item.productName,
          quantity: item.quantity,
          remark: item.remark
        }))
      };

      // 添加确认提示
      proxy.$modal.confirm(`是否确认创建发货单？共 ${totalQuantity} 份`, "提示",
        { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" })
        .then(() => {
          addLgShipment(form).then(response => {
            proxy.$modal.msgSuccess("发货单创建成功");
            shipmentOpen.value = false;
            getUnshippedOrderListPage(); // 刷新列表
          });
        }).catch(() => { });
    }
  });
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  currentOrderUuid.value = row.uuid;
  detailVisible.value = true;
}


// 倒计时定时器
let timer = null;

// 计算倒计时
const getCountdown = (deliveryTime) => {
  if (!deliveryTime) return '-'; //无交货时间

  const end = new Date(deliveryTime).getTime();
  const now = new Date().getTime();
  const diff = end - now;

  if (diff < 0) return '--'; //已超时

  const days = Math.floor(diff / (24 * 60 * 60 * 1000));
  const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
  const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));
  const seconds = Math.floor((diff % (60 * 1000)) / 1000);

  let result = '';
  if (days > 0) result += `${days}天`;
  if (hours > 0) result += `${hours}时`;
  if (minutes > 0) result += `${minutes}分`;
  result += `${seconds}秒`;

  return result;
};

// 判断是否紧急（小于5小时）
const isUrgent = (deliveryTime) => {
  if (!deliveryTime) return false;

  const end = new Date(deliveryTime).getTime();
  const now = new Date().getTime();
  const diff = end - now;

  return diff > 0 && diff < 5 * 60 * 60 * 1000;
};

// 启动定时器
const startTimer = () => {
  // 清除可能存在的旧定时器
  if (timer) clearInterval(timer);

  // 每秒更新一次
  timer = setInterval(() => {
    // 获取当前选中的行
    const selectedRows = unshippedOrderList.value.filter(item => ids.value.includes(item.uuid));

    // 更新数据但保持引用不变
    unshippedOrderList.value.forEach(item => {
      // 触发视图更新但不改变引用
      item._timestamp = Date.now();
    });

    // 确保选中状态保持
    if (selectedRows.length > 0) {
      nextTick(() => {
        selectedRows.forEach(row => {
          const index = unshippedOrderList.value.findIndex(item => item.uuid === row.uuid);
          if (index !== -1) {
            proxy.$refs.tableRef.toggleRowSelection(unshippedOrderList.value[index], true);
          }
        });
      });
    }
  }, 1000);
};

onMounted(() => {
  getUnshippedOrderListPage();
  getLogisticsCompanyList();
  startTimer();
});

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style lang="scss" scoped>
.search-container {
  margin-bottom: 20px;
}

.text-red {
  color: #f56c6c;
}

.text-danger {
  color: var(--el-color-danger);
}

:deep(.el-table .shipped-row) {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
