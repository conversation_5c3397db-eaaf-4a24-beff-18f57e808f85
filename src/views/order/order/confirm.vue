<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">订单{{ isFromEdit ? '修改' : '' }}确认</span>
        </div>
      </template>

      <!-- 订单基本信息 -->
      <div class="section">
        <el-descriptions :column="3" border  label-width="110px">
          <el-descriptions-item label="客户名称">{{ orderForm?.customerName }}</el-descriptions-item>
          <el-descriptions-item label="联系人/电话">{{ orderForm?.contact }}    {{ orderForm?.phone }}</el-descriptions-item>
          <el-descriptions-item label="配送地址">{{ orderForm?.isPickup === 'Y' ? '自提' : orderForm?.address }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            {{ getDictLabel(m209, String(orderForm?.priority)) }}
          </el-descriptions-item>
          <el-descriptions-item label="订单份数">{{ orderForm?.orderNum }}</el-descriptions-item>
          <el-descriptions-item label="成品尺寸">{{ orderForm?.finishedSize }}</el-descriptions-item>
          <el-descriptions-item label="交货时间">
            {{ parseTime(orderForm?.deliveryTime) || '' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户要求" :span="3">
            {{ orderForm?.customerRequire || '' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>


      <!-- 打印明细 -->
      <div class="section" v-if="layouts?.printList && layouts.printList.length > 0">
        <div class="section-header">
          <h3>印刷明细</h3>
        </div>

        <div v-for="(item, index) in layouts.printList" :key="index" class="print-item">
          <el-card class="part-card">
            <template #header>
              <div class="part-header">
                <span class="part-name">{{ item.partName }}</span>
                <el-tag size="small" :type="item.isMakeup === 'Y' ? 'success' : 'info'">
                  {{ item.isMakeup === 'Y' ? '需要拼版' : '无需拼版' }}
                </el-tag>
              </div>
            </template>
            <el-row>
              <el-col :span="18">
                <!-- 部件基本信息 -->
                <el-descriptions :column="3" border size="small">
                  <el-descriptions-item label="文件尺寸">
                    {{ item.paperWidth }}×{{ item.paperHeight }}mm
                  </el-descriptions-item>
                  <el-descriptions-item label="每贴页数" v-if="item.showPagesPerSig">
                    {{ item.pagesPerSig }}
                  </el-descriptions-item>
                  <el-descriptions-item label="P数">{{ item.pages }}页</el-descriptions-item>
                  <el-descriptions-item label="纸张">{{ item.paperName || '未选择' }}</el-descriptions-item>
                  <el-descriptions-item label="印刷色彩">
                    {{ getDictLabel(m204, String(item.isColorPrint)) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="单双面">
                    {{ getDictLabel(m205, String(item.isSinglePrint)) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="纸张尺寸">{{ item.makeupSize }}</el-descriptions-item>
                  <!-- <el-descriptions-item label="印刷机型号">{{ item.printerType }}</el-descriptions-item> -->
                </el-descriptions>

                <!-- 拼版方案选择 -->
                <template v-if="item.isMakeup === 'Y' && item.dataWrapper">
                  <div class="imposition-section">
                    <div class="section-title">拼版方案选择</div>
                    <div class="imposition-content">
                      <!-- 传统拼版方案 -->
                      <template v-if="item.dataWrapper.traditional && item.dataWrapper.traditional.length > 0 && item.dataWrapper.traditional[0].pageLayout && item.dataWrapper.traditional[0].pageLayout.length > 0">
                        <!-- <div class="scheme-type-title">传统拼版方案</div> -->
                        <div v-for="(pageLayout, layoutIndex) in item.dataWrapper.traditional[0].pageLayout"
                             :key="'traditional-'+layoutIndex"
                             class="layout-group">
                          <!-- 拼版方案头部信息 -->
                          <div class="layout-header">
                            <div class="header-info">
                              <span class="info-item">拼数: {{ pageLayout.ups }}</span>
                              <span class="info-item">列: {{ pageLayout.nupX }}</span>
                              <span class="info-item">行: {{ pageLayout.nupY }}</span>
                            </div>
                            <div class="price-info">
                              <template v-if="isSpecialImpose(pageLayout)">
                                <span class="price-label">单价：</span>
                                <span class="price-value" :class="{ 'special': item.makeupId === layoutIndex }">
                                  {{pageLayout.pageLayoutUnitPrice }}
                                </span>
                                <span class="price-label">数量：</span>
                                <span class="price-value" :class="{ 'special': item.makeupId === layoutIndex }">
                                  {{pageLayout.pageLayoutQty.toFixed(2) }}<span v-if="item?.machineType === '1'"> 米</span>
                                </span>
                                <span class="price-label">总价：</span>
                                <span class="price-value" :class="{ 'special': item.makeupId === layoutIndex }">
                                  {{pageLayout.pageLayoutTotalAmount.toFixed(2) }}
                                </span>
                              </template>
                            </div>
                          </div>

                          <!-- 表格形式显示拼版方案 -->
                          <div class="layout-table">
                            <table>
                              <thead>
                                <tr>
                                  <th style="width: 60px">
                                    <template v-if="isSpecialImpose(pageLayout)">
                                      <el-radio v-model="item.makeupId" :label="layoutIndex"
                                        @change="() => handleMakeupSelect(index, pageLayout)" class="layout-radio">
                                      </el-radio>
                                    </template>
                                  </th>
                                  <th>印面</th>
                                  <th v-if="item?.machineType === '1'">轮转尺寸</th>
                                  <th>拼版类型</th>
                                  <th>旋转90度</th>
                                  <template v-if="!isSpecialImpose(pageLayout)">
                                    <th>单价</th>
                                    <th>数量</th>
                                    <th>总价</th>
                                  </template>
                                </tr>
                              </thead>
                              <tbody>
                                <tr v-for="(layout, layoutDetailIndex) in pageLayout.layouts"
                                    :key="layoutDetailIndex"
                                    :class="{
                                      'is-selected': isSpecialImpose(pageLayout) ?
                                        item.makeupId === layoutIndex :
                                        item.makeupId === `${index}-${layoutIndex}-${layoutDetailIndex}`
                                    }">
                                  <td>
                                    <template v-if="!isSpecialImpose(pageLayout)">
                                      <el-radio v-model="item.makeupId"
                                        :label="`${index}-${layoutIndex}-${layoutDetailIndex}`"
                                        @change="() => handleMakeupSelectF2(index, layoutIndex, pageLayout, layout)"
                                        class="layout-radio">
                                      </el-radio>
                                    </template>
                                  </td>
                                  <td>{{ layout.surface }}</td>
                                  <td v-if="item?.machineType === '1'">{{ layout.rollCutSize }}</td>
                                  <td>{{ getImposeTypeLabel(layout.imposetype) }}</td>
                                  <td>{{ layout.isRotate ? '是' : '否' }}</td>
                                  <template v-if="!isSpecialImpose(pageLayout)">
                                    <td>{{ layout.pageLayoutUnitPrice }}</td>
                                    <td>{{ layout.pageLayoutQty }}</td>
                                    <td>{{ layout.pageLayoutTotalAmount }}</td>
                                  </template>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </template>

                      <!-- 混合拼版方案 -->
                      <template v-if="item.dataWrapper.hybrid && item.dataWrapper.hybrid.length > 0">
                        <!-- <div class="scheme-type-title">混合拼版方案</div> -->
                        <template v-for="(hybrid, hybridIndex) in item.dataWrapper.hybrid" :key="'hybrid-group-'+hybridIndex">
                          <div v-if="hybrid.pageLayout && hybrid.pageLayout.length > 0">
                            <div v-for="(pageLayout, layoutIndex) in hybrid.pageLayout"
                                :key="'hybrid-'+hybridIndex+'-'+layoutIndex"
                                class="layout-group">
                              <!-- 拼版方案头部信息 -->
                              <div class="layout-header">
                                <div class="header-info">
                                  <span class="info-item">拼数: {{ pageLayout.ups }}</span>
                                </div>
                                <div class="price-info">
                                  <template v-if="isSpecialImpose(pageLayout)">
                                    <span class="price-label">单价：</span>
                                    <span class="price-value" :class="{ 'special': item.makeupId === `hybrid-${hybridIndex}-${layoutIndex}` }">
                                      {{pageLayout.pageLayoutUnitPrice }}
                                    </span>
                                    <span class="price-label">数量：</span>
                                    <span class="price-value" :class="{ 'special': item.makeupId === `hybrid-${hybridIndex}-${layoutIndex}` }">
                                      {{pageLayout.pageLayoutQty }}
                                    </span>
                                    <span class="price-label">总价：</span>
                                    <span class="price-value" :class="{ 'special': item.makeupId === `hybrid-${hybridIndex}-${layoutIndex}` }">
                                      {{pageLayout.pageLayoutTotalAmount }}
                                    </span>
                                  </template>
                                </div>
                              </div>

                              <!-- 表格形式显示拼版方案 -->
                              <div class="layout-table">
                                <table>
                                  <thead>
                                    <tr>
                                      <th style="width: 60px">
                                        <template v-if="isSpecialImpose(pageLayout)">
                                          <el-radio v-model="item.makeupId" :label="`hybrid-${hybridIndex}-${layoutIndex}`"
                                            @change="() => handleMakeupSelect(index, pageLayout)" class="layout-radio">
                                          </el-radio>
                                        </template>
                                      </th>
                                      <th>印面</th>
                                      <!-- <th>印数</th> -->
                                      <th>拼版类型</th>
                                      <th>旋转90度</th>
                                      <template v-if="!isSpecialImpose(pageLayout)">
                                        <th>单价</th>
                                        <th>数量</th>
                                        <th>总价</th>
                                      </template>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr v-for="(layout, layoutDetailIndex) in pageLayout.layouts"
                                        :key="layoutDetailIndex"
                                        :class="{
                                          'is-selected': isSpecialImpose(pageLayout) ?
                                            item.makeupId === `h-${hybridIndex}-${layoutIndex}` :
                                            item.makeupId === `h-${hybridIndex}-${layoutIndex}-${layoutDetailIndex}`
                                        }">
                                      <td>
                                        <template v-if="!isSpecialImpose(pageLayout)">
                                          <el-radio v-model="item.makeupId"
                                            :label="`hybrid-${hybridIndex}-${layoutIndex}-${layoutDetailIndex}`"
                                            @change="() => handleMakeupSelectF2(index, layoutIndex, pageLayout, layout)"
                                            class="layout-radio">
                                          </el-radio>
                                        </template>
                                      </td>
                                      <td>{{ layout.surface }}</td>
                                      <!-- <td>{{ layout.qty }}</td> -->
                                      <td>{{ getImposeTypeLabel(layout.imposetype) }}</td>
                                      <td>{{ layout.isRotate ? '是' : '否' }}</td>
                                      <template v-if="!isSpecialImpose(pageLayout)">
                                        <td>{{ layout.pageLayoutUnitPrice }}</td>
                                        <td>{{ layout.pageLayoutQty }}</td>
                                        <td>{{ layout.pageLayoutTotalAmount }}</td>
                                      </template>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </template>
                      </template>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="imposition-section">
                    <div class="section-title">印刷方案</div>
                    <div class="imposition-content">
                      <div class="layout-group">
                        <div class="layout-header">
                          <div class="header-info">
                            <span class="info-item">不拼版</span>
                            <span class="info-item">{{ getDictLabel(m205, String(item.isSinglePrint)) }}</span>
                          </div>
                          <div class="price-info">
                            <span class="price-label">单价：</span>
                            <span class="price-value special">{{ item.printUnitPrice }}</span>
                            <span class="price-label">总价：</span>
                            <span class="price-value special">{{ (item.printUnitPrice * item.pages *
                              orderForm.orderNum).toFixed(2) }}</span>
                          </div>
                        </div>
                        <div class="layout-table">
                          <table>
                            <thead>
                              <tr>
                                <th>印面</th>
                                <th>印数</th>
                                <th>P数</th>
                                <th>单价</th>
                                <th>总价</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr class="is-selected">
                                <td>{{ getDictLabel(m205, String(item.isSinglePrint)) }}</td>
                                <td>{{ orderForm.orderNum }}</td>
                                <td>{{ item.pages }}</td>
                                <td>{{ item.printUnitPrice }}</td>
                                <td>{{ (item.printUnitPrice * item.pages * orderForm.orderNum).toFixed(2) }}</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-col>
              <!-- 印刷工艺信息 -->
              <el-col :span="6">
                <div class="craft-section" v-if="item.printCraftList && item.printCraftList.length > 0">
                  <div class="section-title">后期制作</div>
                  <div class="craft-list">
                    <div v-for="(craft, craftIndex) in getSelectedCrafts(item.printCraftList)"
                      :key="'craft-price-' + craftIndex" class="craft-item">
                      <div class="craft-content">
                        <div class="craft-info">
                          <span class="craft-name">{{ craft.craftName }}</span>
                          <!-- 修改工艺信息中的价格公式tooltip显示 -->
                          <div v-if="craft.formulaScript" class="craft-formula">
                            <span class="formula-label">价格公式：</span>
                            <el-tooltip placement="top">
                              <template #content>
                                <div>
                                  <div v-if="craft.formulaScriptDesc">公式: {{ craft.formulaScriptDesc }}</div>
                                  <!-- <div>原始公式: {{ craft.formulaScript }}</div> -->
                                  <div v-if="craft.filledFormula">填充数值: {{ craft.filledFormula }}</div>
                                </div>
                              </template>
                              <span class="formula-text">{{ craft.formulaScriptDesc || craft.formulaScript }}</span>
                            </el-tooltip>
                          </div>
                          <div v-if="craft.minPrice && craft.minPrice > 0" class="craft-min-price">
                            <span class="min-price-label">保底价：</span>
                            <span class="min-price-value">¥{{ craft.minPrice }}</span>
                          </div>
                        </div>
                        <div class="craft-price">
                          <div class="price-row">
                            <span class="price-label">单价：</span>
                            <span class="price-value">¥{{ craft.unitPrice }}</span>
                          </div>
                          <div class="price-row total">
                            <span class="price-label">小计：</span>
                            <span class="price-value">¥{{ calculateCraftPrice(craft, orderForm.orderNum) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>


          </el-card>

        </div>

      </div>

      <!-- 装订和后期制作信息 + 价格汇总 -->
      <el-row :gutter="24">
        <el-col :span="16">
          <!-- 装订和后期制作信息 -->
          <div class="section" v-if="layouts?.craftList && layouts.craftList.length > 0">
            <div class="section-header">
              <h3>装订</h3>
            </div>
            <div v-for="(item, index) in layouts.craftList" :key="index" class="craft-group">
              <el-card class="craft-card">
                <template #header>
                  <div class="card-header">
                    <span>{{ item.partName }}</span>
                  </div>
                </template>
                <el-row :gutter="20">
                  <!-- 装订工艺 -->
                  <el-col :span="12" v-if="selectedBindingCrafts[index]?.bindingList?.length > 0">
                    <div class="craft-section">
                      <div class="section-title">装订工艺</div>
                      <div class="craft-list">
                        <div v-for="craft in selectedBindingCrafts[index].bindingList" :key="craft.craftUuid"
                          class="craft-item">
                          <div class="craft-content">
                            <div class="craft-info">
                              <span class="craft-name">{{ craft.craftName }}</span>
                              <!-- 修改工艺信息中的价格公式tooltip显示 -->
                              <!-- <div v-if="craft.formulaScript" class="craft-formula">
                                <span class="formula-label">价格公式：</span>
                                <el-tooltip placement="top">
                                  <template #content>
                                    <div>
                                      <div v-if="craft.formulaScriptDesc">中文描述: {{ craft.formulaScriptDesc }}</div>
                                      <div>原始公式: {{ craft.formulaScript }}</div>
                                      <div v-if="craft.filledFormula">填充数值: {{ craft.filledFormula }}</div>
                                    </div>
                                  </template>
                                  <span class="formula-text">{{ craft.formulaScriptDesc || craft.formulaScript }}</span>
                                </el-tooltip>
                              </div>
                              <div v-if="craft.minPrice && craft.minPrice > 0" class="craft-min-price">
                                <span class="min-price-label">保底价：</span>
                                <span class="min-price-value">¥{{ craft.minPrice }}</span>
                              </div> -->
                            </div>
                            <div class="craft-price">
                              <div class="price-row">
                                <span class="price-label">单价：</span>
                                <span class="price-value">¥{{ craft.unitPrice }}</span>
                              </div>
                              <!-- <div class="price-row" v-if="craft.billUnit">
                                <span class="price-label">数量：</span>
                                <span class="price-value">{{ craft.craftPriceContent }} {{ craft.billUnit }}</span>
                              </div> -->
                              <div class="price-row">
                                <span class="price-label">份数：</span>
                                <span class="price-value">{{ orderForm.orderNum }}</span>
                              </div>
                              <div class="price-row total">
                                <span class="price-label">总价：</span>
                                <span class="price-value">¥{{ calculateCraftPrice(craft, orderForm.orderNum) }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-col>

                  <!-- 后期制作 -->
                  <el-col :span="12" v-if="selectedLaterCrafts[index]?.laterMarkList?.length > 0">
                    <div class="craft-section">
                      <div class="section-title">后期制作</div>
                      <div class="craft-list">
                        <div v-for="craft in selectedLaterCrafts[index].laterMarkList" :key="craft.craftUuid"
                          class="craft-item">
                          <div class="craft-content">
                            <div class="craft-info">
                              <span class="craft-name">{{ craft.craftName }}</span>
                            </div>
                            <div class="craft-price">
                              <div class="price-row">
                                <span class="price-label">单价：</span>
                                <span class="price-value">¥{{ craft.unitPrice }}</span>
                              </div>
                              <!-- <div class="price-row" v-if="craft.billUnit">
                                <span class="price-label">数量：</span>
                                <span class="price-value">{{ craft.craftPriceContent }} {{ craft.billUnit }}</span>
                              </div> -->
                              <div class="price-row">
                                <span class="price-label">份数：</span>
                                <span class="price-value">{{ orderForm.orderNum }}</span>
                              </div>
                              <div class="price-row total">
                                <span class="price-label">总价：</span>
                                <span class="price-value">¥{{ calculateCraftPrice(craft, orderForm.orderNum) }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-col>

        <el-col :span="8">
          <!-- 价格汇总 -->
          <div class="price-summary">
            <div class="summary-content">
              <!-- 印刷费用 -->
              <div class="summary-section">
                <div class="section-title">印刷费用</div>
                <div class="summary-items">
                  <div v-for="(item, index) in layouts?.printList" :key="index" class="summary-item">
                    <span class="item-name">
                      {{ item.partName }}
                      <template v-if="item.isMakeup === 'Y'">
                        <span class="layout-info">
                          ({{ item.makeupPageLayout?.nupX }}×{{ item.makeupPageLayout?.nupY }})
                        </span>
                      </template>
                    </span>
                    <span class="item-price" :class="{ 'highlight': item.isMakeup === 'Y' }">
                      ¥{{ (item.isMakeup === 'Y' ?
                        item.makeupPageLayout?.pageLayoutTotalAmount :
                        item.printUnitPrice * item.pages * orderForm.orderNum)?.toFixed(2) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 印刷后期制作费用 -->
              <div class="summary-section" v-if="hasPrintCrafts">
                <div class="section-title">印刷后期--费用</div>
                <div class="summary-items">
                  <template v-for="(item, index) in layouts?.printList" :key="index">
                    <template v-if="item.printCraftList && item.printCraftList.length > 0">
                      <div v-for="craft in getSelectedCrafts(item.printCraftList)" :key="craft.craftUuid"
                        class="summary-item">
                        <span class="item-name">{{ item.partName }} - {{ craft.craftName }}</span>
                        <span class="item-price">¥{{ calculateCraftPrice(craft, orderForm.orderNum) }}</span>
                      </div>
                    </template>
                  </template>
                </div>
              </div>

              <!-- 装订和后期制作费用 -->
              <div class="summary-section" v-if="hasBindingOrLaterCrafts">
                <div class="section-title">装订--费用</div>
                <div class="summary-items">
                  <!-- 装订工艺费用 -->
                  <template v-for="(item, index) in selectedBindingCrafts" :key="'binding-'+index">
                    <div v-for="craft in item.bindingList" :key="craft.craftUuid" class="summary-item">
                      <span class="item-name">{{ item.partName }} - {{ craft.craftName }}</span>
                      <span class="item-price">¥{{ calculateCraftPrice(craft, orderForm.orderNum) }}</span>
                    </div>
                  </template>
                  <!-- 后期制作费用 -->
                  <template v-for="(item, index) in selectedLaterCrafts" :key="'later-'+index">
                    <div v-for="craft in item.laterMarkList" :key="craft.craftUuid" class="summary-item">
                      <span class="item-name">{{ item.partName }} - {{ craft.craftName }}</span>
                      <span class="item-price">¥{{ calculateCraftPrice(craft, orderForm.orderNum) }}</span>
                    </div>
                  </template>
                </div>
              </div>

              <div class="total-price">
                <div class="price-row">
                  <span class="price-label">订单总价：</span>
                  <span class="price">¥{{ originalAmount.toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="handleBack">返回编辑</el-button>
        <el-radio-group v-model="prePressStatus" class="prepress-radio">
          <el-radio label="Y" :disabled="!canPrePress">直接拼版</el-radio>
          <el-radio label="N">暂不拼版</el-radio>
        </el-radio-group>
        <el-tooltip content="直接拼版（印前完成）：需要确保所有打印项都已上传文件" placement="top">
          <span class="radio-tooltip-trigger">
            <el-icon><QuestionFilled /></el-icon>
          </span>
        </el-tooltip>
        <el-button type="primary" @click="handleSubmit"
          :loading="submitting" :disabled="!hasValidImpositionLayouts" >
          {{ submitting ? '提交中...' : '提交订单' }}
        </el-button>
        <el-button type="warning" @click="showSettlement" :loading="settlementLoading" :disabled="settlementLoading">
          {{ settlementLoading ? '生成中...' : '查看结算单' }}
        </el-button>

      </div>
    </el-card>

    <!-- 添加结算单组件 -->
    <Settlement
      v-model:visible="settlementVisible"
      :data="settlementData"
    />
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch, onBeforeMount, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useOrderStore } from '@/stores/order';
import { addOrder, updateOrder } from "@/api/order/order";
import FormulaParser from '@/utils/formulaParser';
import * as math from 'mathjs';
import { QuestionFilled } from '@element-plus/icons-vue';
import { parseTime } from "@/utils/ruoyi";
import useTagsViewStore from '@/store/modules/tagsView';
// 引入结算单组件
import Settlement from './settlement.vue';

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const orderStore = useOrderStore();

// 字典数据
const { m204, m205, pay_type, m209 } = proxy.useDict("m204", "m205", "pay_type", "m209");

// 从Pinia store获取数据
const orderForm = computed(() => orderStore.orderForm);
const selectedProduct = computed(() => orderStore.selectedProduct);
const layouts = computed(() => orderStore.layouts);

// 计算原始总金额
const originalAmount = computed(() => {
  let total = 0;

  // 1. 计算印刷费用（包括拼版和不拼版）
  if (layouts.value?.printList) {
    layouts.value.printList.forEach(item => {
      // 计算印刷费用
      if (item.isMakeup === 'Y' && item.makeupPageLayout) {
        // 拼版方案的总价
        total += Number(item.makeupPageLayout.pageLayoutTotalAmount || 0);
      } else {
        // 不拼版的总价 = 单价 * 页数 * 份数
        const unitPrice = Number(item.printUnitPrice || 0);
        const pages = Number(item.pages || 0);
        const orderNum = Number(orderForm.value?.orderNum || 0);
        total += unitPrice * pages * orderNum;
      }

      // 计算印刷后期制作费用
      if (item.printCraftList?.length > 0) {
        item.printCraftList.forEach(craft => {
          if (craft.printCraftChecked === 'Y') {
            total += Number(calculateCraftPrice(craft, orderForm.value.orderNum));
          }
        });
      }
    });
  }

  // 2. 计算装订工艺费用
  if (layouts.value?.craftList) {
    layouts.value.craftList.forEach(item => {
      // 计算装订工艺费用 - 只计算选中的工艺
      if (item.bindingList) {
        item.bindingList.forEach(craft => {
          if (craft.printCraftChecked === 'Y') {
            total += Number(calculateCraftPrice(craft, orderForm.value.orderNum));
          }
        });
      }

      // 计算后期制作费用
      if (item.laterMarkList) {
        item.laterMarkList.forEach(craft => {
          if (craft.printCraftChecked === 'Y') {
            total += Number(calculateCraftPrice(craft, orderForm.value.orderNum));
          }
        });
      }
    });
  }

  // 返回保留两位小数的总价
  return Number(total.toFixed(2));
});



const submitting = ref(false);

// 计算属性
const selectedBindingCrafts = computed(() => {
  if (!orderForm.value?.craftList) return [];
  return orderForm.value.craftList.map(item => ({
    ...item,
    // 只显示被选中的装订工艺
    bindingList: item.bindingList?.filter(craft => craft.printCraftChecked === 'Y'
    ) || []
  }));
});
const selectedLaterCrafts = computed(() => {
  if (!orderForm.value?.craftList) return [];
  return orderForm.value.craftList.map(item => ({
    ...item,
    laterMarkList: item.laterMarkList?.filter(craft => craft.printCraftChecked === 'Y') || []
  }));
});

// 添加新的计算属性
const hasPrintCrafts = computed(() => {
  return layouts.value?.printList?.some(item =>
    item.printCraftList &&
    item.printCraftList.some(craft => craft.printCraftChecked === 'Y')
  );
});

const hasBindingOrLaterCrafts = computed(() => {
  return (selectedBindingCrafts.value?.some(item => item.bindingList?.length > 0) ||
    selectedLaterCrafts.value?.some(item => item.laterMarkList?.length > 0));
});

// 添加计算属性检查是否所有需要拼版的打印项都有有效的拼版方案
const hasValidImpositionLayouts = computed(() => {
  if (!layouts.value?.printList) return false;

  // 检查每个需要拼版的打印项
  return layouts.value.printList.every(item => {
    // 如果不需要拼版，则跳过检查
    if (item.isMakeup !== 'Y') return true;

    // 检查是否有makeupId和makeupPageLayout
    return item.makeupId !== undefined &&
           item.makeupId !== null &&
           item.makeupPageLayout !== null;
  });
});

// 获取最佳拼版方案
const getBestLayout = (item) => {
  if (!item?.dataWrapper) {
    console.warn('拼版数据不存在');
    proxy.$modal.msgWarning("拼版数据不存在");
    return null;
  }

  // 用于存储所有拼版方案的数组
  let allLayouts = [];

  // 收集传统拼版方案
  if (item.dataWrapper.traditional?.length > 0) {
    item.dataWrapper.traditional.forEach(traditional => {
      if (traditional.pageLayout?.length > 0) {
        traditional.pageLayout.forEach((layout, layoutIndex) => {
          if (layout && layout.layouts && layout.layouts.length > 0) {
            // 添加标记，用于区分传统和混合拼版
            layout.layoutType = 'traditional';
            layout.layoutIndex = layoutIndex; // 用于后续makeupId赋值
            layout.hybridIndex = -1; // 传统拼版无hybrid索引
            // 确保数字属性为数字类型
            layout.ups = Number(layout.ups) || 0;
            layout.pageLayoutTotalAmount = Number(layout.pageLayoutTotalAmount) || 0;
            layout.nupX = Number(layout.nupX) || 0;
            layout.nupY = Number(layout.nupY) || 0;
            allLayouts.push(layout);
          }
        });
      }
    });
  }

  // 收集混合拼版方案
  if (item.dataWrapper.hybrid?.length > 0) {
    item.dataWrapper.hybrid.forEach((hybrid, hybridIndex) => {
      if (hybrid.pageLayout?.length > 0) {
        hybrid.pageLayout.forEach((layout, layoutIndex) => {
          if (layout && layout.layouts && layout.layouts.length > 0) {
            // 添加标记，用于区分传统和混合拼版
            layout.layoutType = 'hybrid';
            layout.hybridIndex = hybridIndex;
            layout.layoutIndex = layoutIndex; // 保存索引用于后续makeupId赋值
            // 确保数字属性为数字类型
            layout.ups = Number(layout.ups) || 0;
            layout.pageLayoutTotalAmount = Number(layout.pageLayoutTotalAmount) || 0;
            layout.nupX = Number(layout.nupX) || 0;
            layout.nupY = Number(layout.nupY) || 0;
            allLayouts.push(layout);
          }
        });
      }
    });
  }

  // 如果没有拼版方案，显示提示并返回null
  if (allLayouts.length === 0) {
    console.warn(`未找到任何拼版方案 - 部件名称: ${item.partName}`);
    proxy.$modal.msgError(`未找到任何拼版方案 - ${item.partName}`);
    return null;
  }

  // 调试日志: 列出所有拼版方案
  console.log('所有拼版方案:');
  allLayouts.forEach((layout, index) => {
    const layoutId = layout.layoutType === 'traditional' ?
      layout.layoutIndex : `hybrid-${layout.hybridIndex}-${layout.layoutIndex}`;
    console.log(`#${index+1}. ${layoutId} [${layout.layoutType}] - ups:${layout.ups}, nupX:${layout.nupX}, nupY:${layout.nupY}, 总价:${layout.pageLayoutTotalAmount.toFixed(1)}`);
  });

  // 计算每个方案的关键指标
  allLayouts.forEach(layout => {
    // 确保数字类型
    layout.ups = Number(layout.ups) || 0;

    // 每个拼版方案下的印面总数
    layout.totalSurfaceCount = layout.layouts?.length || 0;

    // 检查重复拼板(SNR)的存在
    const snrLayouts = (layout.layouts || []).filter(l => l.imposetype === 'SNR');
    layout.hasSNR = snrLayouts.length > 0;
    layout.snrCount = snrLayouts.length;

    // 检查裁切堆叠(CNS)的存在
    const cnsLayouts = (layout.layouts || []).filter(l => l.imposetype === 'CNS');
    layout.hasCNS = cnsLayouts.length > 0;
    layout.cnsCount = cnsLayouts.length;

    // 面数计算 - 所有印面数量
    // 针对特殊拼版，更精确地计算实际面数
    if (isSpecialImpose(layout)) {
      // 特殊拼版方案可能包含多个印面，需要计算总数
      let totalSurfaces = 0;
      // 汇总所有layouts中的面数
      layout.layouts.forEach(l => {
        // 对于特殊拼版，每个layout的surface表示印面数
        totalSurfaces += Number(l.surface) || 1;
      });
      layout.surfaceCount = totalSurfaces;

      // 确保ups值是最新的
      // 特殊拼版的ups值可能需要从layouts中获取
      if (!layout.ups && layout.layouts && layout.layouts.length > 0) {
        // 如果layout本身没有ups值，尝试从第一个子layout获取
        layout.ups = layout.nupX * layout.nupY || layout.layouts[0].ups || 0;
      }
    } else {
      // 普通拼版方案，面数就是layout数量
      layout.surfaceCount = layout.layouts?.length || 0;
    }

    // 调试每个布局的详细信息
    console.log(`布局详情 - ups:${layout.ups}, 类型:${layout.layoutType}, 总价:${layout.pageLayoutTotalAmount.toFixed(2)}, ` +
      `面数:${layout.surfaceCount}, 有SNR:${layout.hasSNR}, SNR数量:${layout.snrCount}, nupX×nupY:${layout.nupX}×${layout.nupY}`);
  });

  // 特殊处理：确保ups计算正确，不受数据类型影响
  const maxUps = Math.max(...allLayouts.map(layout => Number(layout.ups)));
  console.log('找到最大的ups值:', maxUps);

  // 按照优先级排序:
  // 1. 优先选择ups最大的
  // 2. ups相同时，优先选择传统拼版（traditional）而非混合拼版（hybrid）
  // 3. 类型相同时，选择面数最少的（totalSurfaceCount最小）
  // 4. 面数相同时，优先选择有SNR(重复拼板)的
  allLayouts.sort((a, b) => {
    // 1. 首先按ups (拼数)排序，从大到小
    // 确保使用数字比较，避免字符串比较错误
    const aUps = Number(a.ups);
    const bUps = Number(b.ups);
    if (aUps !== bUps) {
      return bUps - aUps; // 降序排列，拼数大的排前面
    }

    // 2. ups相同时，优先选择传统拼版（traditional）
    if (a.layoutType !== b.layoutType) {
      return a.layoutType === 'traditional' ? -1 : 1; // 传统拼版优先
    }

    // 3. 类型相同时，选择面数最少的
    if (a.surfaceCount !== b.surfaceCount) {
      return a.surfaceCount - b.surfaceCount; // 面数少的排前面
    }

    // 4. 面数相同时，优先选择有SNR的
    if (a.hasSNR !== b.hasSNR) {
      return a.hasSNR ? -1 : 1; // 有SNR的排前面
    }

    // 5. 如果SNR都有，优先选择SNR数量多的
    if (a.hasSNR && b.hasSNR && a.snrCount !== b.snrCount) {
      return b.snrCount - a.snrCount; // SNR数量多的排前面
    }

    // 6. 最后比较价格，价格低的优先
    return a.pageLayoutTotalAmount - b.pageLayoutTotalAmount;
  });

  // 调试日志: 排序后的拼版方案
  console.log('排序后的拼版方案（按优先级）:');
  allLayouts.forEach((layout, index) => {
    const layoutId = layout.layoutType === 'traditional' ?
      layout.layoutIndex : `hybrid-${layout.hybridIndex}-${layout.layoutIndex}`;
    console.log(`#${index+1}. ${layoutId} [${layout.layoutType}] - ups:${layout.ups}, 面数:${layout.surfaceCount}, SNR:${layout.hasSNR ? '是' : '否'}, 总价:${layout.pageLayoutTotalAmount.toFixed(1)}`);
  });

  // 取排序后的第一个作为最佳方案
  const bestLayout = allLayouts[0];

  if (bestLayout) {
    // 为makeupId赋正确的值
    if (bestLayout.layoutType === 'traditional') {
      // 传统拼版方案的makeupId就是layoutIndex
      bestLayout.makeupId = bestLayout.layoutIndex;
    } else if (bestLayout.layoutType === 'hybrid') {
      // 混合拼版方案的makeupId需要拼接
      bestLayout.makeupId = `hybrid-${bestLayout.hybridIndex}-${bestLayout.layoutIndex}`;
    }

    // 详细打印选中的最佳方案信息
    console.log('✓ 最终选择的最佳拼版方案:', {
      layoutType: bestLayout.layoutType,
      hybridIndex: bestLayout.hybridIndex,
      layoutIndex: bestLayout.layoutIndex,
      makeupId: bestLayout.makeupId,
      ups: bestLayout.ups,
      nupX: bestLayout.nupX,
      nupY: bestLayout.nupY,
      totalAmount: bestLayout.pageLayoutTotalAmount,
      surfaceCount: bestLayout.surfaceCount,
      hasSNR: bestLayout.hasSNR,
      snrCount: bestLayout.snrCount,
      layouts: bestLayout.layouts?.map(l => ({
        surface: l.surface,
        imposetype: l.imposetype,
        imposeDescription: l.imposeDescription,
        isRotate: l.isRotate,
        rollCutSize: l.rollCutSize
      }))
    });

    console.log(`选中的makeupId: ${bestLayout.makeupId}`);
  }

  return bestLayout;
};

// 初始化默认拼版方案
const initDefaultLayout = () => {
  // 添加更多的防护检查
  if (!layouts.value || !layouts.value.printList || !Array.isArray(layouts.value.printList)) {
    console.warn('No valid layouts data found');
    return;
  }

  try {
    layouts.value.printList.forEach((item, index) => {
      if (!item) return;

      if (item.isMakeup === 'Y' && item.dataWrapper) {
        // 如果还没有选择拼版方案，或者是从add页面过来的新数据
        if (!item.makeupId || !item.makeupPageLayout) {
          try {
            // 获取最佳拼版方案
            const bestLayout = getBestLayout(item);

            if (bestLayout) {
              console.log('找到最佳拼版方案:', {
                ups: bestLayout.ups,
                nupX: bestLayout.nupX,
                nupY: bestLayout.nupY,
                makeupId: bestLayout.makeupId,
                totalAmount: bestLayout.pageLayoutTotalAmount,
                layoutType: bestLayout.layoutType,
                layouts: bestLayout.layouts?.map(l => ({
                  surface: l.surface,
                  imposetype: l.imposetype,
                  imposeDescription: l.imposeDescription,
                  rollCutSize: l.rollCutSize
                }))
              });

              // 自动选择最佳拼版方案
              if (bestLayout.layoutType === 'traditional') {
                // 传统拼版方案
                if (isSpecialImpose(bestLayout)) {
                  // 特殊拼版方案(F2-2或F4开头)
                  console.log('选择特殊传统拼版方案');
                  item.makeupId = bestLayout.layoutIndex;
                  handleMakeupSelect(index, bestLayout);
                } else if (bestLayout.layouts && bestLayout.layouts.length > 0) {
                  // 普通传统拼版方案(F2-1等)
                  console.log('选择普通传统拼版方案');
                  // 选中明细行中surface最小、总价最低的那一行
                  let bestDetailIndex = 0;
                  let bestDetail = bestLayout.layouts[0];
                  for (let i = 1; i < bestLayout.layouts.length; i++) {
                    const l = bestLayout.layouts[i];
                    if (
                      l.surface < bestDetail.surface ||
                      (l.surface === bestDetail.surface && (
                        // 优先SNR
                        (l.imposetype === 'SNR' && bestDetail.imposetype !== 'SNR') ||
                        // 如果都不是SNR或都是SNR，再比总价
                        (l.imposetype === bestDetail.imposetype && l.pageLayoutTotalAmount < bestDetail.pageLayoutTotalAmount)
                      ))
                    ) {
                      bestDetail = l;
                      bestDetailIndex = i;
                    }
                  }
                  item.makeupId = `${index}-${bestLayout.layoutIndex}-${bestDetailIndex}`;
                  handleMakeupSelectF2(index, bestLayout.layoutIndex, bestLayout, bestDetail);
                }
              } else if (bestLayout.layoutType === 'hybrid') {
                // 混合拼版方案
                const hybridIndex = bestLayout.hybridIndex;
                const layoutIndex = bestLayout.layoutIndex;

                if (isSpecialImpose(bestLayout)) {
                  // 特殊混合拼版方案
                  console.log('选择特殊混合拼版方案');
                  item.makeupId = `hybrid-${hybridIndex}-${layoutIndex}`;
                  handleMakeupSelect(index, bestLayout);
                } else if (bestLayout.layouts && bestLayout.layouts.length > 0) {
                  // 普通混合拼版方案
                  console.log('选择普通混合拼版方案');
                  // 选中明细行中surface最小、总价最低的那一行
                  let bestDetailIndex = 0;
                  let bestDetail = bestLayout.layouts[0];
                  for (let i = 1; i < bestLayout.layouts.length; i++) {
                    const l = bestLayout.layouts[i];
                    if (
                      l.surface < bestDetail.surface ||
                      (l.surface === bestDetail.surface && l.pageLayoutTotalAmount < bestDetail.pageLayoutTotalAmount)
                    ) {
                      bestDetail = l;
                      bestDetailIndex = i;
                    }
                  }
                  item.makeupId = `hybrid-${hybridIndex}-${layoutIndex}-${bestDetailIndex}`;
                  handleMakeupSelectF2(index, layoutIndex, bestLayout, bestDetail);
                }
              }

              console.log(`成功选择方案，设置makeupId: ${item.makeupId}`);

              // 直接打印debugInfo
              console.log('打印项信息:', {
                makeupId: item.makeupId,
                makeupPageLayout: item.makeupPageLayout ? '已设置' : '未设置',
                selected: item.makeupId !== undefined && item.makeupId !== null
              });
            } else {
              console.warn('未找到合适的拼版方案');
            }
          } catch (error) {
            console.error('处理拼版方案时出错:', error);
          }
        } else {
          console.log(`打印项 ${index} 已有拼版方案，makeupId: ${item.makeupId}`);
        }
      }
    });
  } catch (error) {
    console.error('初始化默认拼版方案时出错:', error);
  }
};

// 检查数据完整性
onBeforeMount(() => {
  console.log('Confirm page mounted, checking data...', {
    hasOrderData: orderStore.hasOrderData,
    orderForm: orderForm.value,
    selectedProduct: selectedProduct.value,
    layouts: layouts.value
  });

  if (!orderStore.hasOrderData) {
    proxy.$modal.msgError("订单数据不完整，请重新填写");
    router.push('/order/order/add');
    return;
  }
});

// 修改现有的 onMounted
onMounted(async () => {
  try {
    // 初始化默认拼版方案
    initDefaultLayout();

    // 计算所有部件的总价
    calculateTotalAmount();

    // 开发环境下添加调试工具
    if (process.env.NODE_ENV === 'development') {
      window.settlementDebug = {
        showData: () => console.log('订单数据:', orderForm.value, '布局数据:', layouts.value),
        showSettlement: () => showSettlement()
      };
      console.log('💡 调试工具已注册到 window.settlementDebug');
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
    proxy.$modal.msgError("初始化数据失败");
    router.push('/order/order/add');
  }
});

// 获取字典标签
const getDictLabel = (dict, value) => {
  if (!dict || !value) return '';
  const item = dict.find(d => parseInt(d.value) === parseInt(value));
  return item ? item.label : '';
};

// 获取拼版类型中文名称
const getImposeTypeLabel = (type) => {
  switch (type) {
    case 'CNS':
      return '裁切堆叠';
    case 'SNR':
      return '重复拼板';
    case 'NS':
      return '顺序拼板';
    default:
      return type;
  }
};

// 判断是否是特殊拼版方案
const isSpecialImpose = (pageLayout) => {
  if (!pageLayout || !pageLayout.layouts || !Array.isArray(pageLayout.layouts)) return false;
  return pageLayout.layouts.some(l =>
    l && l.imposeDescription && (
      l.imposeDescription.startsWith('DP-F2-2') ||
      l.imposeDescription.startsWith('DP-F4')
    )
  );
};

// 处理特殊拼版方案选择
const handleMakeupSelect = (index, pageLayout) => {
  if (!pageLayout || !layouts.value || !layouts.value.printList || !layouts.value.printList[index]) {
    console.warn('Invalid data for handleMakeupSelect');
    return;
  }

  const item = layouts.value.printList[index];

  console.log('Selecting special imposition layout:', pageLayout);

  // 特殊拼版方案使用 pageLayout 级别的价格
  item.makeupPageLayout = {
    pageLayoutQty: pageLayout.pageLayoutQty || 0,
    pageLayoutTotalAmount: Number(pageLayout.pageLayoutTotalAmount || 0),
    pageLayoutUnitPrice: Number(pageLayout.pageLayoutUnitPrice || 0),
    ups: pageLayout.ups || 0,
    nupX: pageLayout.nupX || 0,
    nupY: pageLayout.nupY || 0,
    simplexDuplex: pageLayout.simplexDuplex || '',
    mediaRotate: pageLayout.mediaRotate || '',
    cutFeed: pageLayout.cutFeed || '',
    pageOrder: pageLayout.pageOrder || '',
    layouts: Array.isArray(pageLayout.layouts) ? pageLayout.layouts.map(l => ({
      layoutId: l.layoutId || '',
      surface: l.surface || 0,
      qty: l.qty || 0,
      isRotate: l.isRotate || false,
      rollCutSize: l.rollCutSize || 0,
      imposetype: l.imposetype || '',
      imposeid: l.imposeid || '',
      optimal: l.optimal || false,
      imposeDescription: l.imposeDescription || '',
      pageLayoutQty: l.pageLayoutQty || 0,
      mediaRotate: l.mediaRotate,
      cutFeed: l.cutFeed,
      pageOrder: l.pageOrder
    })) : []
  };
  // 更新总价
  calculateTotalAmount();
};

// 处理普通拼版方案选择
const handleMakeupSelectF2 = (index, layoutIndex, pageLayout, layout) => {
  if (!layout || !pageLayout || !layouts.value || !layouts.value.printList || !layouts.value.printList[index]) {
    console.warn('Invalid data for handleMakeupSelectF2');
    return;
  }

  const item = layouts.value.printList[index];

  console.log('Selecting F2 imposition layout:', layout);

  // 非特殊拼版方案使用选中行的价格
  item.makeupPageLayout = {
    pageLayoutQty: layout.pageLayoutQty || 0,
    pageLayoutTotalAmount: Number(layout.pageLayoutTotalAmount || 0),
    pageLayoutUnitPrice: Number(layout.pageLayoutUnitPrice || 0),
    ups: pageLayout.ups || 0,
    nupX: pageLayout.nupX || 0,
    nupY: pageLayout.nupY || 0,
    simplexDuplex: pageLayout.simplexDuplex || '',
    mediaRotate: pageLayout.mediaRotate || '',
    cutFeed: pageLayout.cutFeed || '',
    pageOrder: pageLayout.pageOrder || '',
    layouts: [{
      layoutId: layout.layoutId || '',
      surface: layout.surface || 0,
      qty: layout.qty || 0,
      isRotate: layout.isRotate || false,
      rollCutSize: layout.rollCutSize || 0,
      imposetype: layout.imposetype || '',
      imposeid: layout.imposeid || '',
      optimal: layout.optimal || false,
      imposeDescription: layout.imposeDescription || '',
      pageLayoutQty: layout.pageLayoutQty || 0
    }]
  };

  // 更新总价
  calculateTotalAmount();
};

// 计算每个部件的总价
const calculateTotalAmount = () => {
  if (!layouts.value?.printList) return;

  layouts.value.printList.forEach(item => {
    let totalAmount = 0;

    // 计算印刷费用
    if (item.isMakeup === 'Y') {
      // 拼版方案的总价
      totalAmount += item.makeupPageLayout?.pageLayoutTotalAmount || 0;
    } else {
      // 不拼版的总价 = 单价 * P数 * 订单份数
      totalAmount += item.printUnitPrice * item.pages * orderForm.value.orderNum;
    }

    // 计算后期制作费用
    if (item.printCraftList && item.printCraftList.length > 0) {
      const craftTotal = item.printCraftList.reduce((sum, craft) => {
        if (craft.printCraftChecked === 'Y') {
          // 统一计算方式：单价  × 订单份数
          return sum + (craft.unitPrice  * orderForm.value.orderNum);
        }
        return sum;
      }, 0);

      totalAmount += craftTotal;
    }

    item.totalAmount = totalAmount;
  });
};


// 返回编辑
const handleBack = () => {
  try {
    // 在返回之前确保数据被保存
    if (orderForm.value) {
      orderStore.saveOrderData({
        orderForm: orderForm.value,
        selectedProduct: selectedProduct.value,
        layouts: layouts.value
      });
    }

    // 根据来源返回不同的页面
    const uuid = orderForm.value?.uuid;
    if (isFromEdit.value && uuid) {
      router.push(`/order/order/edit/${uuid}`);
    } else {
    router.push('/order/order/add');
    }
  } catch (error) {
    console.error('Error while navigating back:', error);
    // 如果出错，仍然尝试返回
    if (isFromEdit.value && orderForm.value?.uuid) {
      router.push(`/order/order/edit/${orderForm.value.uuid}`);
    } else {
    router.push('/order/order/add');
    }

  }
};

// 提交订单
const handleSubmit = async () => {
  // 如果已经在提交中，直接返回
  if (submitting.value) {
    return;
  }

  // 添加拼版方案检查
  if (!hasValidImpositionLayouts.value) {
    proxy.$modal.msgError("存在需要拼版的部件未找到有效的拼版方案，无法提交订单");
    return;
  }

  try {
    submitting.value = true;

    // 构建最终提交的数据
    const submitData = {
      // 基本信息
      customerName: orderForm.value.customerName,
      contact: orderForm.value.contact,
      phone: orderForm.value.phone,
      address: orderForm.value.address,
      addressUuid: orderForm.value.addressUuid,
      isPickup: orderForm.value.isPickup || 'N', // 添加自提字段
      customerUuid: orderForm.value.customerUuid,
      // payType: orderForm.value.payType,
      priority: orderForm.value.priority,
      orderNum: orderForm.value.orderNum,
      finishedSizeName: orderForm.value.finishedSize,
      finishedSizeUuid: orderForm.value.finishedSizeUuid,
      orderAmount: originalAmount.value.toFixed(2), // 订单总价
      amount: originalAmount.value.toFixed(2), // 原始金额
      customerRequire: orderForm.value.customerRequire,
      deliveryTime: parseTime(orderForm.value.deliveryTime, '{y}-{m}-{d} {h}:{i}:{s}'),
      productMainUuid: selectedProduct.value.uuid,
      productName: selectedProduct.value.productName,
      prePressStatus: prePressStatus.value,
      salesUserId: orderForm.value.salesUserId, // 添加销售人员ID

      // 如果是编辑场景，添加uuid
      ...(isFromEdit.value ? { uuid: orderForm.value.uuid } : {}),

      // 打印明细
      printList: layouts.value.printList?.map(item => {
        // 计算印刷费用
        let printAmount = 0;
        if (item.isMakeup === 'Y') {
          printAmount = item.makeupPageLayout?.pageLayoutTotalAmount || 0;
        } else {
          // 不拼版情况：单价 * P数 * 订单份数
          printAmount = item.printUnitPrice * item.pages * orderForm.value.orderNum;
        }

        // 计算印刷后期制作费用
        let craftTotalPrice = 0;
        if (item.printCraftList && item.printCraftList.length > 0) {
          craftTotalPrice = item.printCraftList.reduce((sum, craft) => {
            if (craft.printCraftChecked === 'Y') {
              return sum + Number(calculateCraftPrice(craft, orderForm.value.orderNum));
            }
            return sum;
          }, 0);
        }

        return {
          partId: item.partId,
          partName: item.partName,
          partDetailId: item.partDetailId,
          classUuid: item.classUuid,
          paperUuid: item.paperUuid,
          paperName: item.paperName,
          isMakeup: item.isMakeup, // 是否需要拼版
          paperWidth: item.paperWidth, // 纸张宽度
          paperHeight: item.paperHeight, // 纸张高度
          pages: item.pages, // P数
          isColorPrint: item.isColorPrint, // 是否彩色印刷
          isSinglePrint: item.isSinglePrint, // 是否单面印刷
          makeupSize: item.makeupSize, // 拼版尺寸
          rollCutSize: item.rollCutSize,
          printUnitPrice: item.printUnitPrice,
          printAmount: printAmount.toFixed(2), // 确保将计算好的金额赋值给 printAmount 字段
          craftTotalPrice: craftTotalPrice.toFixed(2), // 印刷后期制作费用
          totalAmount: (printAmount + craftTotalPrice).toFixed(2), // 总金额
          makeupId: item.makeupId, // 拼版方案ID
          imposeType: item.imposeType, // 拼版类型
          layoutPattern: item.layoutPattern, // 拼版模式
          isCut: item.isCut, // 裁切标记
          bindingMargin: item.bindingMargin, // 装订边缘
          bleedMargin: item.bleedMargin, // 出血尺寸
          custFileUuid: item.custFileUuid, // 添加PDF文件UUID
          fileUrl: item.fileUrl, // 添加PDF文件URL
          originalFileName: item.originalFileName, // 添加原始文件名
          freelayout: item.freelayout, // 添加混拼标识
          pagesPerSig: item.pagesPerSig, // 每贴P数
          showPagesPerSig: item.showPagesPerSig, // 是否显示每贴P数
          printerTypeId: item.printerTypeId, // 添加印刷机型号ID
          printerType: item.printerType, // 添加印刷机型号名称

          // 原始拼版数据
          dataWrapper: item.isMakeup === 'Y' ? {
            traditional: item.dataWrapper?.traditional?.map(traditional => ({
              pageId: traditional.pageId,
              pageLayout: traditional.pageLayout?.filter(layout => layout && layout.layouts && layout.layouts.length > 0)
                .map(layout => ({
                  pageLayoutQty: layout.pageLayoutQty,
                  pageLayoutTotalAmount: layout.pageLayoutTotalAmount,
                  pageLayoutUnitPrice: layout.pageLayoutUnitPrice,
                  ups: layout.ups,
                  nupX: layout.nupX,
                  nupY: layout.nupY,
                  simplexDuplex: layout.simplexDuplex,
                  layouts: layout.layouts?.map(l => ({
                    layoutId: l.layoutId,
                    surface: l.surface,
                    qty: l.qty,
                    isRotate: l.isRotate,
                    rollCutSize: l.rollCutSize,
                    imposetype: l.imposetype,
                    imposeid: l.imposeid,
                    optimal: l.optimal || false,
                    imposeDescription: l.imposeDescription,
                    pageLayoutQty: l.pageLayoutQty || 0,
                    mediaRotate: l.mediaRotate,
                    cutFeed: l.cutFeed,
                    pageOrder: l.pageOrder
                  }))
                }))
            })) || [],
            hybrid: (item.dataWrapper?.hybrid || []).map(hybrid => ({
              pageId: hybrid.pageId,
              pageLayout: hybrid.pageLayout?.filter(layout => layout && layout.layouts && layout.layouts.length > 0)
                .map(layout => ({
                  pageLayoutQty: layout.pageLayoutQty,
                  pageLayoutTotalAmount: layout.pageLayoutTotalAmount,
                  pageLayoutUnitPrice: layout.pageLayoutUnitPrice,
                  ups: layout.ups,
                  nupX: layout.nupX,
                  nupY: layout.nupY,
                  simplexDuplex: layout.simplexDuplex,
                  layouts: layout.layouts?.map(l => ({
                    layoutId: l.layoutId,
                    surface: l.surface,
                    qty: l.qty,
                    isRotate: l.isRotate,
                    rollCutSize: l.rollCutSize,
                    imposetype: l.imposetype,
                    imposeid: l.imposeid,
                    optimal: l.optimal || false,
                    imposeDescription: l.imposeDescription,
                    pageLayoutQty: l.pageLayoutQty || 0,
                    mediaRotate: l.mediaRotate,
                    cutFeed: l.cutFeed,
                    pageOrder: l.pageOrder
                  }))
                }))
            }))
          } : null,

            // 选中的拼版方案
            makeupPageLayout: item.isMakeup === 'Y' ? {
              pageLayoutQty: item.makeupPageLayout?.pageLayoutQty,
              pageLayoutTotalAmount: item.makeupPageLayout?.pageLayoutTotalAmount,
              pageLayoutUnitPrice: item.makeupPageLayout?.pageLayoutUnitPrice,
              ups: item.makeupPageLayout?.ups,
              nupX: item.makeupPageLayout?.nupX,
              nupY: item.makeupPageLayout?.nupY,
              simplexDuplex: item.makeupPageLayout?.simplexDuplex,
              mediaRotate: item.makeupPageLayout?.mediaRotate || '',
              cutFeed: item.makeupPageLayout?.cutFeed || '',
              pageOrder: item.makeupPageLayout?.pageOrder || '',
              layouts: item.makeupPageLayout?.layouts?.map(l => ({
                layoutId: l.layoutId,
                surface: l.surface,
                qty: l.qty,
                isRotate: l.isRotate,
                rollCutSize: l.rollCutSize,
                imposetype: l.imposetype,
                imposeid: l.imposeid,
                optimal: l.optimal || false,
                imposeDescription: l.imposeDescription,
                pageLayoutQty: l.pageLayoutQty || 0
              }))
            } : null,

            // 印刷后期制作
            printCraftList: item.printCraftList?.filter(craft => craft.printCraftChecked === 'Y')
              .map(craft => ({
                craftName: craft.craftName,
                craftUuid: craft.craftUuid,
                partDetailId: craft.partDetailId,
                unitPrice: craft.unitPrice,
                billMethod: craft.billMethod,
                billUnit: craft.billUnit,
                printCraftChecked: craft.printCraftChecked,
                checkedIdArr: craft.checkedIdArr,
                minPrice: craft.minPrice,
                formulaScript: craft.formulaScript,
                formulaScriptDesc: craft.formulaScriptDesc,
                craftAmount: Number(calculateCraftPrice(craft, orderForm.value.orderNum))
              }))
          };
        }),

        // 装订和后期制作
        craftList: layouts.value.craftList?.map(item => {
          // 计算装订和后期制作费用
          let craftTotalPrice = 0;

          // 计算装订工艺费用 - 只计算选中的工艺
          if (item.bindingList) {
            craftTotalPrice += item.bindingList
              .filter(craft =>  craft.printCraftChecked === 'Y')
              .reduce((sum, craft) => sum + Number(calculateCraftPrice(craft, orderForm.value.orderNum)), 0);
          }

          // 计算后期制作费用
          if (item.laterMarkList) {
            craftTotalPrice += item.laterMarkList
              .filter(craft => craft.printCraftChecked === 'Y')
              .reduce((sum, craft) => sum + Number(calculateCraftPrice(craft, orderForm.value.orderNum)), 0);
          }

          return {
            partId: item.partId,
            partName: item.partName,
            craftSelected: item.craftSelected,
            craftTotalPrice: craftTotalPrice.toFixed(2),
            // 装订工艺 - 只包含选中的工艺
            bindingList: item.bindingList?.filter(craft => craft.printCraftChecked === 'Y'
            ).map(craft => ({
              craftName: craft.craftName,
              craftUuid: craft.craftUuid,
              craftType: craft.craftType,
              partDetailId: craft.partDetailId,
              unitPrice: craft.unitPrice,
              billMethod: craft.billMethod,
              billUnit: craft.billUnit,
              selectedRadio: craft.selectedRadio,
              formulaScript: craft.formulaScript,
              minPrice: craft.minPrice,
              formulaScriptDesc: craft.formulaScriptDesc
            })),
            // 后期制作
            laterMarkList: item.laterMarkList?.filter(craft => craft.printCraftChecked === 'Y')
              .map(craft => ({
                craftName: craft.craftName,
                craftUuid: craft.craftUuid,
                craftType: craft.craftType,
                partDetailId: craft.partDetailId,
                unitPrice: craft.unitPrice,
                billMethod: craft.billMethod,
                billUnit: craft.billUnit,
                printCraftChecked: craft.printCraftChecked,
                checkedIdArr: craft.checkedIdArr,
                formulaScript: craft.formulaScript,
                minPrice: craft.minPrice,
                formulaScriptDesc: craft.formulaScriptDesc
              }))
          };
        })
      };

      // 打印提交数据
      console.log('订单提交数据:');
      console.log(JSON.stringify(submitData, null, 2));

      const handleSuccess = async (isEditMode) => {
        proxy.$modal.msgSuccess(isEditMode ? "订单修改成功" : "订单提交成功");

        // 1. 清除 store 中的数据
        orderStore.clearOrderData();

        // 2. 清除 localStorage 和 sessionStorage 中的数据
        const storageKeys = [
          'order-data',  // pinia 持久化的 key
          'orderData',
          'selectedProduct',
          'layouts',
          'orderForm',
          'printList',
          'craftList'
        ];

        storageKeys.forEach(key => {
          localStorage.removeItem(key);
          sessionStorage.removeItem(key);
        });

        // 3. 清除 tagsView 中的缓存
        const tagsViewStore = useTagsViewStore();
        const pagesToRemove = ['OrderAdd', 'OrderEdit', 'OrderConfirm'];

        // 从 cachedViews 中移除
        pagesToRemove.forEach(name => {
          const index = tagsViewStore.cachedViews.indexOf(name);
          if (index > -1) {
            tagsViewStore.cachedViews.splice(index, 1);
          }
        });

        // 4. 关闭相关页面
        if (isEditMode) {
          // 编辑模式：关闭所有编辑页面和确认页面
          const visitedViews = tagsViewStore.visitedViews;
          for (const view of visitedViews) {
            if (view.path.includes('/order/order/edit/')) {
              await tagsViewStore.delView(view);
            }
          }
          proxy.$tab.closePage({path: '/order/order/confirm'});
        } else {
          // 新增模式：关闭新增页面和确认页面
          proxy.$tab.closePage({path: '/order/order/add'});
          proxy.$tab.closePage({path: '/order/order/confirm'});
        }

        // 5. 跳转到订单列表
        proxy.$tab.closeOpenPage({path: '/order/order'});
      };

      const confirmMessage = isFromEdit.value
        ? '原订单失效，会生成新的订单，是否提交修改？'
        : '是否确认提交订单？';

      try {
        await proxy.$modal.confirm(confirmMessage, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 用户确认后执行提交
        if (isFromEdit.value) {
          await updateOrder(submitData);
          await handleSuccess(true);
        } else {
          await addOrder(submitData);
          await handleSuccess(false);
        }
      } catch (error) {
        console.log(error);
        if (error === 'cancel') {
          // 用户取消操作
          proxy.$modal.msg("操作已取消");
        } else {
          // 其他错误
          console.error('订单操作失败:', error);
          proxy.$modal.msgError(`订单${isFromEdit.value ? '修改' : '提交'}失败`);
        }
        throw error; // 重新抛出错误，让外层catch处理
      }
    } catch (error) {
      console.error('订单提交失败:', error);
      if (error !== 'cancel') { // 只有非取消操作才显示错误信息
        proxy.$modal.msgError(`订单${isFromEdit.value ? '修改' : '提交'}失败222`);
      }
    } finally {
      submitting.value = false;
    }
  };

  const getSelectedCrafts = (craftList) => {
    if (!craftList) return [];
    return craftList.filter(craft => craft.printCraftChecked === 'Y');
  };

  // 根据工艺找到对应的部件
  const getItemByCraft = (craft) => {
    // 先在printList中查找
    if (layouts.value?.printList) {
      for (const item of layouts.value.printList) {
        if (item.printCraftList) {
          const foundCraft = item.printCraftList.find(c =>
            c.craftUuid === craft.craftUuid &&
            c.printCraftChecked === 'Y'
          );
          if (foundCraft) {
            return item;
          }
        }
      }
    }

    // 如果在printList中找不到，则返回null
    return null;
  };

  // 安全的工艺价格计算方法（用于结算单）
  const calculateCraftPriceSafe = (craft, orderNum) => {
    try {
      // 简化计算，避免复杂的公式解析
      if (!craft || !orderNum) {
        return 0;
      }

      const unitPrice = Number(craft.unitPrice) || 0;
      const minPrice = Number(craft.minPrice) || 0;

      // 简单计算：单价 * 订单数量
      let total = unitPrice * orderNum;

      // 考虑保底价
      if (minPrice > 0) {
        total = Math.max(total, minPrice);
      }

      return total.toFixed(2);
    } catch (error) {
      console.error('安全计算工艺价格失败:', error);
      return 0;
    }
  };

  // 计算工艺价格的方法
  const calculateCraftPrice = (craft, orderNum) => {
    // 从craft中获取关联的item对象
    const item = getItemByCraft(craft);

    // 获取surface值
    let surface = 0;
    if (item && item.isMakeup === 'Y' && item.makeupPageLayout && item.makeupPageLayout.layouts) {
      surface = item.makeupPageLayout.layouts.reduce((sum, layout) => {
        return sum + (Number(layout.surface) || 0);
      }, 0);
    }

    // 准备计算参数
    const params = {
      _pages_: Number(item?.pages) || 0,
      _pagesPerSig_: item?.pagesPerSig !== null && item?.pagesPerSig !== undefined ? Number(item.pagesPerSig) : 0,
      _isSinglePrint_: Number(item?.isSinglePrint) || 0,
      _isColorPrint_: Number(item?.isColorPrint) || 0,
      _surface_: Number(surface) || 0,
      _isMakeup_: item ? (item.isMakeup === 'Y' ? 1 : 0) : 0,
      _orderNum_: Number(orderNum) || 0,
      _unitPrice_: Number(craft.unitPrice) || 0,
      _ups_: item?.makeupPageLayout?.ups || 0,  // 添加ups字段
      // 添加变量别名，使公式更简单
      surface: Number(surface) || 0,
      pages: Number(item?.pages) || 0,
      pagesPerSig: item?.pagesPerSig !== null && item?.pagesPerSig !== undefined ? Number(item.pagesPerSig) : 0,
      orderNum: Number(orderNum) || 0,
      unitPrice: Number(craft.unitPrice) || 0,
      isMakeup: item ? (item.isMakeup === 'Y' ? 1 : 0) : 0,
      ups: item?.makeupPageLayout?.ups || 0  // 添加ups别名
    };

    let total = 0;

    // 减少日志输出，避免性能问题
    if (process.env.NODE_ENV === 'development') {
      console.log('开始计算价格:', {
        craftName: craft.craftName,
        craftType: craft.craftType,
        formulaScript: craft.formulaScript ? '有公式' : '无公式',
        minPrice: craft.minPrice
      });
    }

    // 1. 生成填充了实际数值的公式，用于显示
    if (craft.formulaScript) {
      let filledFormula = craft.formulaScript;
      filledFormula = filledFormula.replace(/_isMakeup_/g, `"${item?.isMakeup || 'N'}"`);
      filledFormula = filledFormula.replace(/\b_pages_\b/g, params._pages_);
      filledFormula = filledFormula.replace(/\b_pagesPerSig_\b/g, params._pagesPerSig_);
      filledFormula = filledFormula.replace(/\b_surface_\b/g, params._surface_);
      filledFormula = filledFormula.replace(/\b_orderNum_\b/g, params._orderNum_);
      filledFormula = filledFormula.replace(/\b_unitPrice_\b/g, params._unitPrice_);
      filledFormula = filledFormula.replace(/\b_ups_\b/g, params._ups_);

      // 保存填充后的公式到craft对象中，用于tooltip显示
      craft.filledFormula = filledFormula;
    }

    // 2. 使用FormulaParser计算公式
    if (craft.formulaScript) {
      // 使用FormulaParser解析和计算公式
      total = FormulaParser.calculate(craft.formulaScript, params, 0);
      if (process.env.NODE_ENV === 'development') {
        console.log('FormulaParser计算结果:', total);
      }
    } else {
      // 3. 没有公式时的默认计算
      if (item) {
        // printList下的后期制作
        if (item.isMakeup === 'Y') {
          // 拼版情况: surface * unitPrice
          total = params._surface_ * craft.unitPrice;
        } else {
          // 不拼版情况: pages * unitPrice * orderNum
          total = params._pages_ * craft.unitPrice * orderNum;
        }
      } else {
        // 装订和后期制作的默认计算
        if (craft.craftType === '1') {
          // 装订工艺: 单价 * 订单份数
          total = craft.unitPrice * orderNum;
        } else if (craft.craftType === '2') {
          // 后期制作: 单价 * 订单份数
          total = craft.unitPrice * orderNum;
        } else {
          // 其他情况: 单价 * 订单份数
          total = craft.unitPrice * orderNum;
        }
      }
    }

    // 4. 考虑保底价
    if (craft.minPrice && craft.minPrice > 0) {
      total = Math.max(Number(total), Number(craft.minPrice));
    }

    // 保存后期制作单项总价到craftAmount字段
    if (craft.craftAmount !== undefined) {
      craft.craftAmount = Number(total.toFixed(2));
    }

    const finalPrice = total.toFixed(2);

    return finalPrice;
  };

  const prePressStatus = ref('N'); // 默认为N

  // 添加计算属性检查是否所有打印项都有文件
  const canPrePress = computed(() => {
    if (!layouts.value?.printList || layouts.value.printList.length === 0) {
      return false;
    }
    // 检查每个打印项是否都有文件
    return layouts.value.printList.every(item =>
      item.custFileUuid && item.fileUrl && item.originalFileName
    );
  });

  // 监听canPrePress变化，如果为false则重置prePressStatus为'N'
  watch(canPrePress, (newVal) => {
    if (!newVal && prePressStatus.value === 'Y') {
      prePressStatus.value = 'N';
    }
  });

  // 在 script setup 部分添加
  const isFromEdit = computed(() => route.query.from === 'edit');

  // 在 setup 中添加结算单相关的响应式变量
  const settlementVisible = ref(false);
  const settlementData = ref({});
  const settlementLoading = ref(false);

  // 在 handleSubmit 函数之前添加显示结算单的方法
  const showSettlement = async () => {
    try {
      // 防止重复点击和加载状态
      if (settlementVisible.value || settlementLoading.value) {
        return;
      }

      settlementLoading.value = true;

      // 简单的数据量检查
      const totalItems = (layouts.value?.printList?.length || 0) + (layouts.value?.craftList?.length || 0);
      if (totalItems > 100) {
        proxy.$modal.msgWarning(`数据量较大（${totalItems}项），生成可能需要稍等片刻`);
      }

      // 添加小延迟，避免阻塞UI线程
      await new Promise(resolve => setTimeout(resolve, 10));

      // 开发环境下进行简单检查
      if (process.env.NODE_ENV === 'development') {
        console.log('=== 开发环境调试 ===');
        console.log('订单数据:', orderForm.value);
        console.log('布局数据:', layouts.value);
      }

      // 简单构建结算单数据
      const submitData = {
        // 基本订单信息
        customerName: orderForm.value.customerName,
        contact: orderForm.value.contact,
        phone: orderForm.value.phone,
        deliveryTime: orderForm.value.deliveryTime,
        address: orderForm.value.address,
        customerRequire: orderForm.value.customerRequire,
        orderNum: orderForm.value.orderNum,
        orderAmount: originalAmount.value,

        // 印刷列表
        printList: layouts.value?.printList || [],

        // 工艺列表
        craftList: layouts.value?.craftList || []
      };

      console.log('结算单数据构建完成');

      // 设置结算单数据
      settlementData.value = submitData;

      // 使用nextTick确保DOM更新完成后再显示对话框
      await nextTick();
      settlementVisible.value = true;

    } catch (error) {
      console.error('生成结算单失败:', error);
      proxy.$modal.msgError('生成结算单失败，请稍后重试');
      settlementVisible.value = false;
      settlementData.value = {};
    } finally {
      settlementLoading.value = false;
    }
  };

</script>

<style lang="scss" scoped>
// 基础布局
.app-container {
  padding: 20px;

  .section {
    margin-bottom: 24px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .section-header {
      margin-bottom: 16px;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2f3d;
      }
    }
  }
}

// 打印明细部分
.print-item {
  margin-bottom: 20px;

  .part-card {
    .part-header {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      background: #fafafa;

      .part-name {
        font-size: 15px;
        font-weight: 500;
        color: #1f2f3d;
      }
    }
  }
}

// 拼版方案部分
.imposition-section {
  margin-top: 20px;

  .section-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #1f2f3d;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .layout-group {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;

    .layout-header {
      padding: 8px 16px;
      background: #f5f7fa;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-info {
        display: flex;
        gap: 16px;

        .info-item {
          color: #606266;
          font-weight: 500;
        }
      }

      .price-info {
        display: flex;
        gap: 8px;
        align-items: center;

        .price-label {
          color: #606266;
          font-size: 14px;
        }

        .price-value {
          color: #606266;
          font-weight: 500;
          font-size: 15px;

          &.special {
            color: #f56c6c;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }

    .layout-table {
      width: 100%;

      table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 8px;
          text-align: center;
          font-size: 13px;
          color: #606266;
          border-bottom: 1px solid #ebeef5;
          white-space: nowrap;
        }

        th {
          background-color: #f5f7fa;
          font-weight: 500;
        }

        tbody tr {
          &:hover {
            background-color: #f5f7fa;
          }

          &.is-selected {
            background-color: #f0f9ff;

            td {
              color: #409eff;

              &:nth-last-child(1),
              &:nth-last-child(2) {
                color: #f56c6c;
                font-weight: 500;
              }
            }
          }

          .layout-radio {
            margin: 0;
          }
        }
      }
    }
  }
}

// 工艺部分
.craft-section {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  height: 100%;

  .section-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #1f2f3d;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .craft-list {
    .craft-item {
      margin-bottom: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background: #f0f9ff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      }

      .craft-content {
        .craft-info {
          margin-bottom: 8px;
          padding-bottom: 8px;
          border-bottom: 1px dashed #e0e0e0;

          .craft-name {
            font-weight: 500;
            color: #1f2f3d;
            font-size: 14px;
          }
        }

        .craft-price {
          .price-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            font-size: 13px;

            .price-label {
              color: #606266;
            }

            .price-value {
              color: #606266;
              font-weight: 500;
            }

            &.total {
              margin-top: 8px;
              padding-top: 8px;
              border-top: 1px dashed #e0e0e0;

              .price-value {
                color: #f56c6c;
                font-size: 15px;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}

// 价格汇总部分
.price-summary {
  height: 100%;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .summary-content {
    padding: 20px;

    .summary-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 15px;
        font-weight: 500;
        color: #1f2f3d;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;
      }

      .summary-items {
        .summary-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px dashed #dcdfe6;

          &:last-child {
            border-bottom: none;
          }

          .item-name {
            color: #606266;
            font-size: 14px;
            flex: 1;
            margin-right: 10px;
            word-break: break-all;

            .layout-info {
              color: #909399;
              font-size: 13px;
              margin-left: 4px;
            }
          }

          .item-price {
            color: #f56c6c;
            font-weight: 500;
            font-size: 15px;
            white-space: nowrap;

            &.highlight {
              font-weight: 600;
              font-size: 16px;
            }
          }
        }
      }
    }

    .total-price {
      margin-top: 20px;
      padding: 20px;
      border-top: 2px solid #409eff;
      background: #f8f9fa;
      border-radius: 4px;

      .price-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        font-weight: 500;

        .price-label {
          color: #606266;
        }

        .price {
          color: #f56c6c;
          font-weight: 600;
          font-size: 24px;
        }
      }
    }
  }
}

// 操作按钮
.action-buttons {
  margin-top: 24px;
  text-align: center;
  padding: 24px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .el-button {
    min-width: 120px;
    margin: 0 12px;
  }

  .prepress-radio {
    margin: 0 20px;
    vertical-align: middle;
  }

  .radio-tooltip-trigger {
    margin-left: 8px;
    color: #909399;
    cursor: help;
    vertical-align: middle;

    .el-icon {
      vertical-align: middle;
      font-size: 16px;
    }
  }
}

.scheme-type-title {
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
  margin: 16px 0 12px;
  padding-left: 8px;
  border-left: 3px solid #409eff;
  line-height: 1.2;

  &:first-child {
    margin-top: 0;
  }
}

.hybrid-group-title {
  font-size: 13px;
  font-weight: 500;
  color: #606266;
  margin: 12px 0 8px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: inline-block;
}

.craft-formula {
  font-size: 12px;
  margin-top: 4px;
  color: #606266;

  .formula-label {
    color: #909399;
  }

  .formula-text {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: bottom;
    cursor: pointer;
    color: #409EFF;
    text-decoration: underline;
  }
}

.craft-min-price {
  font-size: 12px;
  margin-top: 2px;
  color: #F56C6C;

  .min-price-label {
    color: #909399;
  }

  .min-price-value {
    font-weight: 500;
  }
}

.craft-start-price {
  font-size: 12px;
  margin-top: 2px;
  color: #E6A23C;

  .start-price-label {
    color: #909399;
  }

  .start-price-value {
    font-weight: 500;
  }
}
</style>

