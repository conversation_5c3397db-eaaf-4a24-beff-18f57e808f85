<template>
  <el-form-item :label="label" :prop="prop" :label-width="labelWidth">
    <div class="pdf-upload-container">
      <div class="upload-section">
        <el-upload class="pdf-upload" :before-upload="beforeUpload" accept=".pdf" action="#" :limit="1"
          :http-request="handleUpload" :on-exceed="handleExceed" :on-remove="handleRemove" :file-list="fileList"
          :show-file-list="true" :auto-upload="true" list-type="text"
          drag>
          <template #trigger>
            <div class="upload-trigger">
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div class="upload-tip">支持 PDF 格式文件</div>
              <div v-if="modelValue?.pages" class="file-info">
                <span class="pages">共 {{ modelValue.pages }} 页</span>
              </div>
            </div>
          </template>
          <template #tip>
            <div class="pdf-info">
              <div v-if="modelValue?.uploadStatus?.showProgress" class="progress-info">
                <div class="progress-header">
                  <el-progress :percentage="Number(modelValue.uploadStatus.percent)" :format="format"
                    :status="modelValue.uploadStatus.percent >= 100 ? 'success' : ''" />
                  <el-button v-if="modelValue.uploadStatus.percent < 100" type="danger" link @click="cancelUpload">
                    取消上传
                  </el-button>
                </div>
                <div class="upload-info">
                  <span>已上传：{{ modelValue.uploadStatus.loadedSize }}/{{ modelValue.uploadStatus.totalSize }}</span>
                  <span>用时：{{ formatTime(modelValue.uploadStatus.elapsedTime) }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-upload>
      </div>

      <div v-if="modelValue?.pageDimensions" class="dimensions-section">
        <div class="dimensions-header">
          <span class="title">文件尺寸信息</span>
        </div>
        <div class="dimensions-content">
          <div v-for="(group, index) in groupedDimensions" :key="index" class="dimension-group">
            <div class="group-header">
              <span class="page-range">{{ group.range }}</span>
              <span class="dimension">{{ group.dimension }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-form-item>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as qiniu from 'qiniu-js'
import * as pdfjsLib from 'pdfjs-dist'
import moment from 'moment'
import { saveQiniuContent } from '@/api/system/qiniu'
import { UploadFilled } from '@element-plus/icons-vue'

// 设置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`

/**
 * 组件属性定义
 * @property {Object} modelValue - 当前文件上传项的数据模型
 * @property {Number} index - 当前文件在上传列表中的索引
 * @property {Array} printList - 整个打印列表数据
 * @property {String} label - 表单项标签文本
 * @property {String} prop - 表单验证的属性名
 * @property {String} labelWidth - 表单项标签宽度
 * @property {String} token - 七牛云上传token
 * @property {Object} config - 七牛云配置信息
 * @property {String} host - 七牛云域名
 * @property {String} bucket - 七牛云存储空间名
 * @property {String} filePathPrefix - 文件存储路径前缀
 */
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  printList: {
    type: Array,
    required: true
  },
  label: {
    type: String,
    default: '文件上传'
  },
  prop: {
    type: String,
    required: true
  },
  labelWidth: {
    type: String,
    default: '130px'
  },
  token: {
    type: String,
    required: true
  },
  config: {
    type: Object,
    required: true
  },
  host: {
    type: String,
    required: true
  },
  bucket: {
    type: String,
    required: true
  },
  filePathPrefix: {
    type: String,
    required: true
  }
})

// 定义事件发射器，用于向父组件传递数据更新
const emit = defineEmits(['update:printList', 'fileUploaded', 'fileRemoved', 'token-expired'])

// 在 script setup 中添加 fileListRef
const fileListRef = ref([])

// 添加上传订阅引用
const uploadSubscription = ref(null)

/**
 * 计算当前处理的打印项
 * @returns {Object} 当前打印项数据
 */
const currentItem = computed(() => props.printList[props.index])

/**
 * 计算上传文件列表
 * 用于显示已上传的文件
 * @returns {Array} 文件列表数据
 */
const fileList = computed({
  get: () => {
    if (currentItem.value?.fileUrl?.length) {
      return [{
        name: currentItem.value.originalFileName,
        url: currentItem.value.fileUrl[0],
        status: 'success'
      }]
    }
    return []
  },
  set: (val) => {
    fileListRef.value = val
  }
})

/**
 * 上传前的文件验证
 * @param {File} file - 待上传的文件对象
 * @returns {boolean} 是否通过验证
 */
const beforeUpload = (file) => {
  // 检查文件类型
  const isPDF = file.type === 'application/pdf'
  if (!isPDF) {
    ElMessage.error('只能上传PDF文件!')
    return false
  }

  // 检查文件大小（限制为2000MB）
  const isLt100M = file.size / 1024 / 1024 < 2000
  if (!isLt100M) {
    ElMessage.error('文件大小不能超过 2000MB!')
    return false
  }

  // 检查七牛云配置是否完整
  if (!props.token || !props.host || !props.bucket) {
    ElMessage.error('上传配置不完整，请稍后重试!')
    return false
  }

  return true
}

/**
 * 处理超出文件数量限制的情况
 */
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件，请先删除当前文件')
}

/**
 * 计算分组后的尺寸信息
 */
const groupedDimensions = computed(() => {
  if (!props.modelValue?.pageDimensions) return []

  const dimensions = props.modelValue.pageDimensions
  const groups = []
  let currentGroup = {
    start: 1,
    dimension: `${dimensions[0].width} × ${dimensions[0].height} mm`
  }

  for (let i = 1; i < dimensions.length; i++) {
    const currentDim = `${dimensions[i].width} × ${dimensions[i].height} mm`
    if (currentDim !== currentGroup.dimension) {
      groups.push({
        range: currentGroup.start === i ?
          `第 ${currentGroup.start} 页` :
          `第 ${currentGroup.start}-${i} 页`,
        dimension: currentGroup.dimension
      })
      currentGroup = {
        start: i + 1,
        dimension: currentDim
      }
    }
  }

  // 添加最后一组
  groups.push({
    range: currentGroup.start === dimensions.length ?
      `第 ${currentGroup.start} 页` :
      `第 ${currentGroup.start}-${dimensions.length} 页`,
    dimension: currentGroup.dimension
  })

  return groups
})

/**
 * 处理文件移除操作
 */
const handleRemove = (file, uploadFiles) => {
  console.log('Removing file:', file)
  console.log('Current upload files:', uploadFiles)

  const newPrintList = [...props.printList]
  const newItem = { ...newPrintList[props.index] }

  // 发出文件删除事件，只传递文件名
  emit('fileRemoved', {
    index: props.index,
    fileName: newItem.originalFileName
  });

  // 重置文件相关字段
  newItem.custFileUuid = ''
  newItem.pages = null
  newItem.fileUuid = ''
  newItem.fileUrl = []
  newItem.originalFileName = ''
  newItem.widthInMm = null
  newItem.heightInMm = null
  newItem.pageDimensions = null // 添加这行，确保删除尺寸信息
  // 重置上传状态
  newItem.uploadStatus = {
    showProgress: false,
    totalSize: '0 KB',
    loadedSize: '0 KB',
    percent: '0',
    elapsedTime: '0'
  }

  // 更新打印列表
  newPrintList[props.index] = newItem
  emit('update:printList', newPrintList)

  // 清空文件列表
  fileListRef.value = []

  ElMessage.success('文件已删除')
}

/**
 * 将点数转换为毫米
 * @param {number} points - PDF点数（72dpi）
 * @returns {number|null} 转换后的毫米数或null
 */
const convertToMm = (points) => {
  // 检查points是否为有效的数字
  if (typeof points === 'number' && !isNaN(points)) {
    // 将点转换为毫米（1点 = 1/72英寸 = 25.4/72毫米），使用四舍五入
    // 更精确的转换系数：25.4/72 = 0.3527777777777778
    return Math.round(points * (25.4 / 72))
  }
  return null
}

/**
 * 读取PDF文件信息
 * @param {File} file - PDF文件对象
 * @returns {Promise<Object>} 包含页数和尺寸信息的对象
 */
const readPdfPages = async (file) => {
  try {
    // 创建文件的 ArrayBuffer，用于PDF.js读取
    const arrayBuffer = await file.arrayBuffer()

    // 加载 PDF 文档
    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer })
    const pdfDoc = await loadingTask.promise
    const totalPages = pdfDoc.numPages
    console.log('总页数：' + totalPages)

    // 读取所有页面的尺寸信息
    const pageDimensions = []
    for (let i = 1; i <= totalPages; i++) {
      const page = await pdfDoc.getPage(i)
      const viewport = page.getViewport({ scale: 1 }) // 获取原始尺寸（scale=1）

      // 将PDF点数转换为毫米
      const widthInMm = convertToMm(viewport.width)
      const heightInMm = convertToMm(viewport.height)

      if (!widthInMm || !heightInMm) {
        throw new Error(`无法计算第 ${i} 页PDF尺寸`)
      }

      pageDimensions.push({
        width: widthInMm.toFixed(2),
        height: heightInMm.toFixed(2)
      })

      console.log(`第 ${i} 页宽度: ${widthInMm} mm, 高度: ${heightInMm} mm`)
    }

    return {
      pages: totalPages,
      pageDimensions,
      // 保留第一页尺寸作为默认值
      widthInMm: pageDimensions[0].width,
      heightInMm: pageDimensions[0].height
    }
  } catch (error) {
    console.error('读取PDF文件失败:', error)
    throw new Error('读取PDF文件失败: ' + (error.message || '未知错误'))
  }
}

/**
 * 取消文件上传
 */
const cancelUpload = () => {
  if (uploadSubscription.value) {
    uploadSubscription.value.unsubscribe()
    uploadSubscription.value = null
    handleRemove(null, [])
    ElMessage.info('已取消上传')
  }
}

/**
 * 处理文件上传
 * @param {Object} options - 上传选项
 * @param {File} options.file - 待上传的文件
 */
const handleUpload = async ({ file }) => {
  if (!beforeUpload(file)) {
    return;
  }

  // 记录开始时间，用于计算上传耗时
  const begin = Date.now();

  // 生成文件存储路径：按日期分类 + 随机数防重名
  const datePath = moment().format('YYYYMMDD');
  const randomNum = Math.floor(Math.random() * 90000) + 10000;
  const newFileName = `${randomNum}-${file.name}`;
  const filename = `${props.filePathPrefix}/${datePath}/${newFileName}`;

  // 设置七牛上传参数
  const putExtra = {
    fname: filename,
    params: {}
  };

  // 初始化上传状态
  const newPrintList = [...props.printList];
  const newItem = { ...newPrintList[props.index] };
  newItem.uploadStatus = {
    showProgress: true,
    totalSize: formatSize(file.size),
    loadedSize: '0 KB',
    percent: 0,
    elapsedTime: 0
  };
  newPrintList[props.index] = newItem;
  emit('update:printList', newPrintList);

  try {
    // 确保七牛配置正确
    if (!props.token || !props.config) {
      throw new Error('七牛云配置不完整');
    }

    // 使用七牛SDK上传文件
    const observable = qiniu.upload(file, filename, props.token, putExtra, props.config);

    // 保存订阅对象以便后续取消
    uploadSubscription.value = observable.subscribe({
      next: (res) => {
        // 确保 res.total 存在且包含所需属性
        const total = res.total || {};
        const totalSize = total.size || file.size;
        const loadedSize = total.loaded || 0;
        const percent = total.percent || 0;
        const currentTime = Date.now();

        // 更新上传状态
        const updatedPrintList = [...props.printList];
        const updatedItem = { ...updatedPrintList[props.index] };
        updatedItem.uploadStatus = {
          showProgress: true,
          totalSize: formatSize(totalSize),
          loadedSize: formatSize(loadedSize),
          percent: Math.floor(percent),
          elapsedTime: currentTime - begin
        };
        updatedPrintList[props.index] = updatedItem;
        emit('update:printList', updatedPrintList);
      },
      error: (err) => {
        console.error('Upload failed:', err);
        uploadSubscription.value = null;

        // 处理token过期的情况
        if (err.code === 401 && err.data?.error === 'expired token') {
          emit('token-expired'); // 发出token过期事件
          ElMessage.error('上传凭证已过期，正在重新获取，请稍后重试');
        } else {
          ElMessage.error(err.message || '上传失败，请重试');
        }

        handleRemove(file, fileListRef.value);
      },
      complete: async (res) => {
        try {
          // 读取PDF文件信息
          const pdfInfo = await readPdfPages(file);

          const finalPrintList = [...props.printList];
          const finalItem = { ...finalPrintList[props.index] };

          // 更新PDF信息
          if (pdfInfo) {
            finalItem.pages = pdfInfo.pages;
            finalItem.widthInMm = pdfInfo.widthInMm;
            finalItem.heightInMm = pdfInfo.heightInMm;
            finalItem.pageDimensions = pdfInfo.pageDimensions;
          } else {
            throw new Error('无法读取PDF文件信息');
          }

          // 准备保存到服务器的数据
          const qiniuContentData = {
            name: res.key,
            size: finalItem.uploadStatus.totalSize,
            url: `${props.host}/${res.key}`,
            originalFileName: file.name,
            bucket: props.bucket,
            uploadSource: 'MIS',
            pages: finalItem.pages,
            width: finalItem.widthInMm,
            height: finalItem.heightInMm
          };

          // 保存文件信息到服务器
          const saveRes = await saveQiniuContent(qiniuContentData);
          if (saveRes.code === 200) {
            // 更新本地数据
            finalItem.custFileUuid = saveRes.data.uuid;
            finalItem.fileUuid = saveRes.data.uuid;
            finalItem.fileUrl = [saveRes.data.url];
            finalItem.originalFileName = file.name;
            finalPrintList[props.index] = finalItem;
            emit('update:printList', finalPrintList);

            // 通知父组件文件上传完成，需要重新计算每贴页数
            emit('fileUploaded', {
              index: props.index,
              pages: finalItem.pages,
              fileName: file.name,
              fileUrl: finalItem.fileUrl,
              fileUuid: finalItem.fileUuid
            });

            ElMessage.success('文件上传成功');
          } else {
            throw new Error(saveRes.msg || '保存文件信息失败');
          }
          uploadSubscription.value = null;
        } catch (error) {
          console.error('Save file info failed:', error);
          ElMessage.error(error.message || '保存文件信息失败');
          uploadSubscription.value = null;
          handleRemove(file, fileListRef.value);
        }
      }
    });
  } catch (error) {
    console.error('Upload initialization failed:', error);
    ElMessage.error('上传初始化失败，请重试');
    uploadSubscription.value = null;
    handleRemove(file, fileListRef.value);
  }
};

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小（KB或MB）
 */
const formatSize = (bytes) => {
  if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(2)} KB`
  }
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
}

/**
 * 格式化耗时
 * @param {number} ms - 毫秒数
 * @returns {string} 格式化后的时间
 */
const formatTime = (ms) => {
  if (ms < 1000) return `${ms}ms`
  return `${(ms / 1000).toFixed(1)}s`
}

/**
 * 格式化上传进度显示
 * @param {number} percentage - 上传进度百分比
 * @returns {string} 格式化后的进度显示
 */
const format = (percentage) => {
  return percentage === 100 ? '完成' : `${percentage}%`
}
</script>

<style scoped>
.pdf-upload-container {
  display: flex;
  gap: 12px;
  width: 100%;
}

.upload-section {
  flex: 1;
}

.pdf-upload {
  width: 100%;
}

.pdf-upload :deep(.el-upload) {
  width: 100%;
}

.pdf-upload :deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.el-icon--upload {
  font-size: 32px;
  color: #909399;
  margin-bottom: 8px;
}

.el-upload__text {
  font-size: 14px;
  margin-bottom: 4px;
}

.el-upload__text em {
  color: #409EFF;
  font-style: normal;
  margin: 0 4px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.dimensions-section {
  flex: 1;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 8px;
}

.dimensions-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e4e7ed;
}

.dimensions-header .title {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.dimensions-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dimension-group {
  background-color: #fff;
  border-radius: 4px;
  padding: 6px 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-range {
  color: #606266;
  font-size: 12px;
}

.dimension {
  color: #409EFF;
  font-weight: 500;
  font-size: 12px;
}

.pdf-info {
  margin-top: 8px;
}

.file-info {
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-info {
  margin-top: 8px;
}

.progress-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-header :deep(.el-progress) {
  flex: 1;
  margin-right: 8px;
}

.pages {
  color: #409EFF;
  margin-right: 12px;
  display: inline-block;
  margin-top: 6px;
  font-size: 13px;
}

.upload-info {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
}

.page-dimensions {
  margin-top: 8px;
  padding: 6px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.dimension-item {
  margin-bottom: 3px;
  color: #606266;
}

.dimension-item:last-child {
  margin-bottom: 0;
}
</style>
