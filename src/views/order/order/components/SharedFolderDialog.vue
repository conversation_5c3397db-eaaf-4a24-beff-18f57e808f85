<template>
  <el-dialog
      v-model="dialogVisible"
      :title="props.title"
      width="700px"
      :close-on-click-modal="false"
      @close="handleClose"
      class="shared-folder-dialog"
      :append-to-body="true"
      :lock-scroll="true"
      :modal-append-to-body="true"
      top="5vh"
      :destroy-on-close="false"
      draggable
  >
    <div class="shared-folder-container">
      <!-- 共享文件选择提示 -->
      <div class="manual-mode-tips">
        <el-alert
            title="共享文件选择"
            type="info"
            :closable="false"
            show-icon
        >
          <template #default>
            <p>请到资源管理器中打开如下地址</p>
            <p>共享路径：<code>{{ uploadConfig?.uploadPath }}</code></p>
            <p v-if="props.single">请选择一个文件（只能上传一个文件）：</p>
            <p v-else>请选择文件：</p>
          </template>
        </el-alert>

        <div class="manual-actions">
          <el-button type="primary" @click="openSharedFolder" icon="Folder">
            打开共享文件夹
          </el-button>
        </div>

        <!-- 手动输入文件路径 -->
        <div class="manual-input">
          <el-divider content-position="center">输入文件路径（粘贴后自动验证）</el-divider>
          <el-input
              v-model="manualFilePath"
              :placeholder="props.single ? '请粘贴具体文件的完整路径（单文件模式，粘贴后自动验证）' : '请粘贴具体文件的完整路径（粘贴后自动验证，然后点击确认）'"
              clearable
              @keyup.enter="handleManualConfirm"
              @paste="handlePaste"
              type="textarea"
              :rows="2"
          >
            <template #append>
              <el-button @click="handleManualConfirm" :disabled="!manualFilePath.trim()">
                确认选择
              </el-button>
            </template>
          </el-input>
          <div class="manual-input-tip">
            <el-text type="info" size="small">
              💡 支持的文件路径格式：<br>
              • 相对路径：<code>客户A\文件名.pdf</code><br>
              • Mac完整路径：<code>/Volumes/misShare/客户A/文件名.pdf</code><br>
              • Windows完整路径：<code>"\\***********\misShare\客户A\文件名.pdf"</code><br>
              <span v-if="props.single">⚠️ <strong>注意：单文件模式，必须是具体文件路径，选择后会替换之前的文件</strong></span>
              <span v-else>⚠️ <strong>注意：必须是具体文件路径，粘贴后自动验证，然后点击确认按钮</strong></span>
            </el-text>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-info" v-if="selectedFile && !selectedFile.folder">
          <el-text type="info" size="small">
            {{ selectedFile.label }}
          </el-text>
        </div>
        <div class="footer-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button
              type="primary"
              @click="handleConfirm"
              :disabled="!selectedFile || selectedFile.folder"
          >
            {{ props.single ? '确认替换' : '确认选择' }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, onMounted, computed} from 'vue'
import {ElMessage} from 'element-plus'
import {Folder} from '@element-plus/icons-vue'
import {getBusinessUploadType} from '@/api/order/order'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  single: {
    type: Boolean,
    default: true // 默认为单文件模式
  },
  title: {
    type: String,
    default: '选择共享文件'
  }
})
const emit = defineEmits(['update:modelValue', 'select', 'replace'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedFile = ref<any | null>(null)
const uploadConfig = ref<any>(null) // 存储上传配置信息
const manualFilePath = ref('') // 手动输入的文件路径

const loadUploadConfig = async () => {
  try {
    const configResponse = await getBusinessUploadType()
    if (configResponse.code === 200) {
      uploadConfig.value = configResponse.data

      if (!uploadConfig.value.uploadPath) {
        ElMessage.error('未配置共享文件路径')
      }
    } else {
      ElMessage.error('获取上传配置失败')
    }
  } catch (error) {
    ElMessage.error('获取配置失败')
  }
}

// 打开共享文件夹
const openSharedFolder = () => {
  if (!uploadConfig.value?.uploadPath) {
    ElMessage.error('未配置共享文件路径')
    return
  }

  const ua = navigator.userAgent.toLowerCase()
  const sharedPath = uploadConfig.value.uploadPath

  if (ua.includes('windows')) {
    // Windows系统：复制UNC路径
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(sharedPath).then(() => {
        ElMessage.success('共享路径已复制到剪贴板，请在资源管理器地址栏粘贴打开')
      }).catch(() => {
        ElMessage.error('复制失败，请手动复制路径：' + sharedPath)
      })
    } else {
      ElMessage.info('请手动复制路径：' + sharedPath)
    }
  } else if (ua.includes('mac')) {
    // Mac系统：转换为SMB格式
    const smbPath = sharedPath.replace(/\\/g, 'smb://').replace(/\\/g, '/')
    window.open(smbPath, '_blank')
    ElMessage.success('已尝试打开共享文件夹')
  } else {
    // 其他系统
    ElMessage.info('共享路径：' + sharedPath)
  }
}

// 标准化文件路径（处理不同操作系统的路径格式）
const normalizePath = (inputPath: string, sharedPath: string) => {
  let cleanPath = inputPath.trim()

  // 移除首尾的双引号和单引号
  if ((cleanPath.startsWith('"') && cleanPath.endsWith('"')) ||
      (cleanPath.startsWith("'") && cleanPath.endsWith("'"))) {
    cleanPath = cleanPath.slice(1, -1)
  }

  // 修复Mac路径前缀问题：如果以\Volumes开头，替换为/Volumes
  if (cleanPath.startsWith('\\Volumes')) {
    cleanPath = cleanPath.replace('\\Volumes', '/Volumes')
  }

  // 统一路径分隔符为反斜杠
  cleanPath = cleanPath.replace(/\//g, '\\')

  // 从uploadPath中提取共享名（最后一个反斜杠后的部分）
  const shareName = sharedPath.split('\\').pop()

  if (shareName && cleanPath.includes(shareName)) {
    // 找到共享名的位置
    const shareIndex = cleanPath.indexOf(shareName)
    if (shareIndex >= 0) {
      // 提取共享名之后的相对路径部分
      const afterSharePath = cleanPath.substring(shareIndex + shareName.length)
      // 用配置的uploadPath替换前面的部分，返回完整路径
      return sharedPath.replace(/\\+$/, '') + afterSharePath
    }
  }

  // 如果没有找到共享名，可能是相对路径，直接拼接
  const relativePath = cleanPath.replace(/^\\+/, '')
  return sharedPath.replace(/\\+$/, '') + '\\' + relativePath
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  // 延迟处理，确保粘贴的内容已经更新到输入框
  setTimeout(() => {
    const pastedText = manualFilePath.value.trim()
    if (pastedText) {
      // 自动验证粘贴的内容，但不自动确认
      validatePastedPath()
    }
  }, 100)
}

// 验证粘贴的路径（不自动确认）
const validatePastedPath = () => {
  const inputPath = manualFilePath.value.trim()
  if (!inputPath) return

  if (!uploadConfig.value?.uploadPath) {
    ElMessage.error('未配置共享文件路径')
    return
  }

  // 验证输入的是文件路径而不是文件夹路径
  if (!isFilePath(inputPath)) {
    ElMessage.error('请输入具体的文件路径，不能是文件夹路径。文件路径应该包含文件扩展名，如：.pdf、.doc、.jpg 等')
    return
  }

  // 标准化路径，直接返回完整路径
  const fullPath = normalizePath(inputPath, uploadConfig.value.uploadPath)

  if (!fullPath) {
    ElMessage.error('无法解析文件路径，请检查路径格式')
    return
  }

  // 构建SMB路径
  const smbPath = fullPath.replace(/\\\\/g, 'smb://').replace(/\\/g, '/')

  // 提取文件名
  const fileName = fullPath.split('\\').pop() || ''

  // 提取相对路径（用于显示）
  const baseUploadPath = uploadConfig.value.uploadPath.replace(/\\+$/, '')
  const relativePath = fullPath.replace(baseUploadPath + '\\', '')

  // 创建文件对象，但不自动确认
  const fileObj = {
    id: `manual-${Date.now()}`,
    label: fileName,
    path: relativePath,
    fullPath: fullPath,
    smbPath: smbPath,
    folder: false,
    manual: true
  }

  selectedFile.value = fileObj
  const confirmText = props.single ? '确认替换' : '确认选择'
  ElMessage.success(`路径解析成功：${fileObj.label}，请点击${confirmText}按钮完成操作`)

  // 显示解析结果供用户确认
  console.log('路径解析结果:', {
    原始输入: inputPath,
    相对路径: relativePath,
    完整路径: fullPath,
    SMB路径: smbPath,
    文件名: fileName
  })
}

// 验证路径是否为文件（而非文件夹）
const isFilePath = (path: string) => {
  // 检查是否有文件扩展名
  const fileName = path.split(/[/\\]/).pop() || ''
  const hasExtension = fileName.includes('.') && !fileName.endsWith('.')

  // 检查是否以斜杠结尾（通常表示文件夹）
  const endsWithSlash = path.endsWith('/') || path.endsWith('\\')

  return hasExtension && !endsWithSlash
}

// 处理手动输入的文件路径
const handleManualConfirm = () => {
  const inputPath = manualFilePath.value.trim()
  if (!inputPath) {
    ElMessage.warning('请输入文件路径')
    return
  }

  if (!uploadConfig.value?.uploadPath) {
    ElMessage.error('未配置共享文件路径')
    return
  }

  // 验证输入的是文件路径而不是文件夹路径
  if (!isFilePath(inputPath)) {
    ElMessage.error('请输入具体的文件路径，不能是文件夹路径。文件路径应该包含文件扩展名，如：.pdf 等')
    return
  }

  // 标准化路径，直接返回完整路径
  const fullPath = normalizePath(inputPath, uploadConfig.value.uploadPath)

  if (!fullPath) {
    ElMessage.error('无法解析文件路径，请检查路径格式')
    return
  }

  // 构建SMB路径
  const smbPath = fullPath.replace(/\\\\/g, 'smb://').replace(/\\/g, '/')

  // 提取文件名
  const fileName = fullPath.split('\\').pop() || ''

  // 提取相对路径（用于显示）
  const baseUploadPath = uploadConfig.value.uploadPath.replace(/\\+$/, '')
  const relativePath = fullPath.replace(baseUploadPath + '\\', '')

  // 创建文件对象
  const fileObj = {
    id: `manual-${Date.now()}`,
    label: fileName,
    path: relativePath,
    fullPath: fullPath,
    smbPath: smbPath,
    folder: false,
    manual: true
  }

  selectedFile.value = fileObj
  const confirmText = props.single ? '确认替换' : '确认选择'
  ElMessage.success(`路径解析成功：${fileObj.label}，请点击${confirmText}按钮完成操作`)

  // 显示解析结果供用户确认
  console.log('路径解析结果:', {
    原始输入: inputPath,
    相对路径: relativePath,
    完整路径: fullPath,
    SMB路径: smbPath,
    文件名: fileName
  })
}

// 确认选择文件
const handleConfirm = () => {
  if (selectedFile.value && !selectedFile.value.folder) {
    if (props.single) {
      // 单文件模式：发送替换事件
      emit('replace', selectedFile.value)
      ElMessage.success(`已选择文件：${selectedFile.value.label}`)
    } else {
      // 多文件模式：发送选择事件
      emit('select', selectedFile.value)
      ElMessage.success(`已选择文件：${selectedFile.value.label}`)
    }
    dialogVisible.value = false
  }
}

const handleClose = () => {
  selectedFile.value = null
  manualFilePath.value = ''
  dialogVisible.value = false
}

onMounted(() => {
  loadUploadConfig()
})
</script>

<style lang="scss" scoped>
.shared-folder-dialog {
  :deep(.el-dialog) {
    position: fixed !important;
    top: 5vh !important;
    margin: 0 auto !important;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    margin: 0;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-overlay) {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2000;
  }

  // 确保弹框在滚动时保持居中
  :deep(.el-overlay-dialog) {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: flex-start !important;
    justify-content: center !important;
    padding-top: 5vh !important;
    box-sizing: border-box !important;
  }
}

.shared-folder-container {
  height: 70vh;
  max-height: 520px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;

  .manual-mode-tips {
    padding: 16px;

    .el-alert {
      margin-bottom: 16px;

      p {
        margin: 8px 0;
        line-height: 1.5;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      code {
        background: #f1f2f3;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: monospace;
        font-size: 13px;
        color: #e74c3c;
      }
    }

    .manual-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
    }

    .manual-input {
      margin-top: 20px;

      .el-divider {
        margin: 16px 0;
      }

      .manual-input-tip {
        margin-top: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        .el-text {
          line-height: 1.6;

          code {
            background: #e3f2fd;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            color: #1976d2;
            margin: 0 2px;
          }
        }
      }
    }
  }
}
</style>
