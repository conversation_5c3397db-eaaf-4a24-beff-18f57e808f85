<template>
  <el-dialog
    v-model="dialogVisible"
    title="工单打印预览"
    :width="dialogWidth"
    :close-on-click-modal="false"
    append-to-body
    class="work-order-dialog"
  >
    <!-- 固定A5横版 -->
    <div class="size-info">
      <el-tag type="primary" size="large">A5横版打印</el-tag>
    </div>

    <div class="work-order-container size-a5" id="workOrderContent">
      <!-- 工单头部 -->
      <div class="work-order-header">
        <div class="header-content">
          <h1 class="company-name">{{ workOrderData.companyName || '印刷公司' }}</h1>
          <h2 class="document-title">生产工单</h2>
        </div>
      </div>

      <!-- 基本信息与二维码 -->
      <div class="basic-info-with-qr">
        <div class="basic-info">
          <!-- A5横版布局 -->
          <div class="info-row">
            <div class="info-item">
              <span class="label">订单号：</span>
              <span class="value order-no">{{ workOrderData.orderNo }}</span>
            </div>
            <div class="info-item">
              <span class="label">下单时间：</span>
              <span class="value">{{ workOrderData.dealDatetime }}</span>
            </div>
            <div class="info-item">
              <span class="label">交货时间：</span>
              <span class="value">{{ workOrderData.deliveryTime }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">客户姓名：</span>
              <span class="value">{{ workOrderData.customerName }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系方式：</span>
              <span class="value">{{ workOrderData.contact }} / {{ workOrderData.phone }}</span>
            </div>
            <div class="info-item">
              <span class="label">收货地址：</span>
              <span class="value">{{ workOrderData.address }}</span>
            </div>
          </div>
          <div class="info-row" v-if="workOrderData.customerRequire">
            <div class="info-item full-width">
              <span class="label">客户要求：</span>
              <span class="value customer-require-text">{{ workOrderData.customerRequire }}</span>
            </div>
          </div>
        </div>
        <div class="qr-code-container" v-if="workOrderData.orderNo">
          <canvas ref="qrCodeCanvas" width="60" height="60"></canvas>
        </div>
      </div>

      <!-- 生产明细表格 -->
      <div class="content-table">
        <table class="production-table">
          <thead>
            <tr>
              <th>序号</th>
              <th>部件</th>
              <th>文件名</th>
              <th>项目</th>
              <th>拼板/工艺</th>
              <th>P数</th>
              <th>份数</th>
              <th>纸张数</th>
              <th>备注</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in contentRows" :key="index">
              <td>{{ row[0] }}</td>
              <td>{{ row[1] }}</td>
              <td>{{ row[2] }}</td>
              <td class="project-cell">
                <div v-html="formatMultilineText(row[3])"></div>
              </td>
              <td class="craft-cell">
                <div v-html="formatMultilineText(row[4])"></div>
              </td>
              <td>{{ row[5] }}</td>
              <td>{{ row[6] }}</td>
              <td>{{ row[7] }}</td>
              <td>{{ row[8] }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="info" @click="handlePreview">
          <el-icon><View /></el-icon>
          预览打印
        </el-button>
        <el-button type="primary" @click="handleDirectPrint">
          <el-icon><Printer /></el-icon>
          直接打印
        </el-button>
        <el-button type="success" @click="handleDownload">
          <el-icon><Download /></el-icon>
          下载Word
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Printer, Download, View } from '@element-plus/icons-vue'
import QRCode from 'qrcode'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  workOrderData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'download'])

const qrCodeCanvas = ref(null)

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 生成内容表格数据
const contentRows = computed(() => {
  const rows = []
  const data = props.workOrderData

  if (!data.printList) return rows

  // 添加表头
  rows.push(['序号', '部件', '文件名', '项目', '拼板/工艺', 'P数', '份数', '纸张数', '备注'])

  let rowIndex = 0

  // 添加打印内容
  data.printList?.forEach((print, i) => {
    rowIndex++

    // 打印工艺列表
    const printCraftNames = print.printCraftList?.map(craft => craft.craftName).join('\n') || ''

    // 拼板信息
    let layoutInfo = ''
    let imposetypeQty = ''
    let surface = ''

    if (print.makeupPagelayoutJson) {
      try {
        const makeupInfo = JSON.parse(print.makeupPagelayoutJson)
        const ups = makeupInfo.ups
        layoutInfo = `${ups}拼`

        const layouts = makeupInfo.layouts || []
        const imposetypeQtyList = []
        const surfaceList = []

        layouts.forEach(layout => {
          const imposetype = layout.imposetype
          const qty = layout.qty
          const surfaceValue = print.isSinglePrint === 0
            ? Math.ceil(layout.surface / 2)
            : layout.surface

          imposetypeQtyList.push(`${convertImposeType(imposetype)}${qty}份`)
          surfaceList.push(surfaceValue.toString())
        })

        imposetypeQty = imposetypeQtyList.join('\n')
        surface = surfaceList.join('\n')
      } catch (e) {
        console.error('解析拼版信息失败:', e)
      }
    }

    // 不拼板情况
    if (print.isMakeup === 'N') {
      imposetypeQty = `${data.orderNum}份`
    }

    let pagesPerSig = ''
    if (print.pagesPerSig && parseInt(print.pagesPerSig) > 0) {
      pagesPerSig = `\n每贴页数：${print.pagesPerSig}`
    }

    const projectInfo = `${print.paperName}\n文件尺寸：${print.paperWidth}*${print.paperHeight}mm\n${print.isSinglePrint === 1 ? '单面' : '双面'}/${print.isColorPrint === 1 ? '彩色' : '黑白'}`
    const craftInfo = `${layoutInfo}${pagesPerSig}\n${printCraftNames}`

    rows.push([
      `${rowIndex}印刷`,
      print.partName || '',
      print.originalFileName?.trim() || '',
      projectInfo,
      craftInfo,
      print.pages?.toString() || '',
      imposetypeQty,
      surface,
      print.remark || ''
    ])
  })

  // 添加装订和后期工艺
  data.craftList?.forEach(craft => {
    // 装订工艺
    craft.bindingList?.forEach(binding => {
      rowIndex++
      rows.push([
        `${rowIndex}装订`,
        '',
        '',
        binding.craftName?.trim() || '',
        '',
        '',
        data.orderNum?.toString() || '',
        '',
        ''
      ])
    })

    // 后期制作工艺
    craft.laterMarkList?.forEach(laterMark => {
      rowIndex++
      rows.push([
        `${rowIndex}装订`,
        '',
        '',
        laterMark.craftName?.trim() || '',
        '',
        '',
        data.orderNum?.toString() || '',
        '',
        ''
      ])
    })
  })

  return rows.slice(1) // 移除表头，因为表头在模板中定义
})

// 转换拼版类型
const convertImposeType = (imposetype) => {
  const typeMap = {
    'CNS': '裁切堆叠',
    'SNR': '重复拼板',
    'NS': '顺序拼板'
  }
  return typeMap[imposetype] || imposetype
}

// 格式化多行文本
const formatMultilineText = (text) => {
  if (!text) return ''
  return text.replace(/\n/g, '<br>')
}

// 生成二维码
const generateQRCode = async () => {
  if (!props.workOrderData.orderNo || !qrCodeCanvas.value) return

  try {
    await QRCode.toCanvas(qrCodeCanvas.value, props.workOrderData.orderNo, {
      width: 100,
      height: 100,
      margin: 1
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
  }
}

// 监听对话框显示状态
watch(dialogVisible, async (newVal) => {
  if (newVal) {
    await nextTick()
    generateQRCode()
  }
})

// 直接打印 - 适配Windows环境
const handleDirectPrint = () => {
  try {
    const workOrderContent = document.getElementById('workOrderContent')
    if (!workOrderContent) {
      console.error('找不到工单内容元素')
      proxy.$modal.msgError('找不到工单内容，请重试')
      return
    }

    // 确保二维码已生成
    if (!workOrderContent.querySelector('canvas')) {
      generateQRCode()
      setTimeout(() => {
        executeDirectPrint()
      }, 500)
    } else {
      executeDirectPrint()
    }

  } catch (error) {
    console.error('直接打印失败:', error)
    proxy.$modal.msgError('打印失败，请重试')
  }
}

// 执行直接打印
const executeDirectPrint = () => {
  // 添加打印样式
  addPrintStyles()

  // 执行打印
  setTimeout(() => {
    window.print()
    // 打印完成后清理样式
    setTimeout(() => {
      removePrintStyles()
    }, 1000)
  }, 100)
}

// 预览打印 - 在新窗口中预览
const handlePreview = () => {
  try {
    const workOrderContent = document.getElementById('workOrderContent')
    if (!workOrderContent) {
      console.error('找不到工单内容元素')
      proxy.$modal.msgError('找不到工单内容，请重试')
      return
    }

    // 确保二维码已生成
    if (!workOrderContent.querySelector('canvas')) {
      generateQRCode()
      setTimeout(() => {
        executePreview()
      }, 500)
    } else {
      executePreview()
    }

  } catch (error) {
    console.error('预览打印失败:', error)
    proxy.$modal.msgError('预览失败，请重试')
  }
}

// 执行预览
const executePreview = () => {
  const workOrderContent = document.getElementById('workOrderContent')

  // 创建新窗口进行预览
  const previewWindow = window.open('', '_blank', 'width=900,height=700')
  if (!previewWindow) {
    proxy.$modal.msgError('无法打开预览窗口，请检查浏览器弹窗设置')
    return
  }

  const content = workOrderContent.outerHTML
  const htmlContent = generatePreviewHTML(content)

  previewWindow.document.write(htmlContent)
  previewWindow.document.close()

  // 在新窗口中重新生成二维码
  previewWindow.onload = () => {
    setTimeout(() => {
      generateQRCodeInWindow(previewWindow)
    }, 100)
  }
}

// 生成预览HTML
const generatePreviewHTML = (content) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>打印预览 - ${props.workOrderData.orderNo}</title>
      <style>
        @page {
          size: A5 landscape;
          margin: 5mm;
        }

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: "Microsoft YaHei", "SimSun", serif;
          font-size: 6px;
          line-height: 1.0;
          color: #000;
          background: white;
          padding: 3mm;
        }

        .work-order-header {
          margin-bottom: 2mm;
          padding-bottom: 1mm;
          border-bottom: 1px solid #000;
          text-align: center;
        }

        .company-name {
          font-weight: bold;
          margin: 0 0 1mm 0;
          font-size: 8px;
        }

        .document-title {
          font-weight: bold;
          margin: 0;
          font-size: 6px;
        }

        .basic-info-with-qr {
          display: flex;
          margin-bottom: 2mm;
          gap: 8px;
          align-items: flex-start;
        }

        .basic-info {
          flex: 1;
          margin-bottom: 1mm;
        }

        .info-row {
          margin-bottom: 1mm;
        }

        .info-item {
          display: inline-block;
          margin-right: 5mm;
          vertical-align: top;
        }

        .info-item.full-width {
          display: block;
          margin-right: 0;
          margin-bottom: 1mm;
        }

        .label {
          font-weight: bold;
          margin-right: 1mm;
        }

        .value {
          color: #000;
        }

        .order-no {
          font-weight: bold;
          color: #000;
        }

        .qr-code-container {
          flex-shrink: 0;
          width: 12mm;
          height: 12mm;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        canvas {
          width: 12mm !important;
          height: 12mm !important;
          border: 1px solid #000;
          display: block;
        }

        .production-table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 2mm;
        }

        .production-table th,
        .production-table td {
          border: 1px solid #000;
          padding: 0.5mm;
          text-align: center;
          vertical-align: top;
          line-height: 1.0;
          font-size: 5px;
        }

        .production-table th {
          background-color: #f0f0f0;
          font-weight: bold;
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }

        .project-cell,
        .craft-cell {
          text-align: left;
          font-size: 4px;
        }

        /* 列宽设置 */
        .production-table th:nth-child(1),
        .production-table td:nth-child(1) { width: 8%; }
        .production-table th:nth-child(2),
        .production-table td:nth-child(2) { width: 10%; }
        .production-table th:nth-child(3),
        .production-table td:nth-child(3) { width: 15%; }
        .production-table th:nth-child(4),
        .production-table td:nth-child(4) { width: 20%; }
        .production-table th:nth-child(5),
        .production-table td:nth-child(5) { width: 18%; }
        .production-table th:nth-child(6),
        .production-table td:nth-child(6) { width: 6%; }
        .production-table th:nth-child(7),
        .production-table td:nth-child(7) { width: 8%; }
        .production-table th:nth-child(8),
        .production-table td:nth-child(8) { width: 8%; }
        .production-table th:nth-child(9),
        .production-table td:nth-child(9) { width: 7%; }

        @media print {
          body {
            margin: 0;
            padding: 3mm;
          }
        }
      </style>
    </head>
    <body>
      ${content}
      <div style="margin-top: 10mm; text-align: center; font-size: 10px; color: #666;">
        使用浏览器的打印功能（Ctrl+P 或 Cmd+P）进行打印
      </div>
    </body>
    </html>
  `
}

// 处理下载
const handleDownload = () => {
  emit('download')
  dialogVisible.value = false
}

// 在新窗口中生成二维码
const generateQRCodeInWindow = async (targetWindow) => {
  try {
    const canvas = targetWindow.document.querySelector('canvas')
    if (!canvas) {
      console.error('预览窗口中找不到canvas元素')
      return
    }

    const qrText = `订单号:${props.workOrderData.orderNo}`

    await QRCode.toCanvas(canvas, qrText, {
      width: 48,
      height: 48,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
  } catch (error) {
    // 二维码生成失败时静默处理
  }
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 添加打印样式 - 简化版
const addPrintStyles = () => {
  const styleId = 'simple-print-styles'
  // 移除已存在的样式
  const existingStyle = document.getElementById(styleId)
  if (existingStyle) {
    existingStyle.remove()
  }

  const style = document.createElement('style')
  style.id = styleId
  style.textContent = `
    @media print {
      @page {
        size: 210mm 148mm; /* 明确指定A5横版尺寸 */
        margin: 5mm;
        orientation: landscape;
      }

      html, body {
        width: 210mm !important;
        height: 148mm !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
      }

      * {
        visibility: hidden !important;
      }

      #workOrderContent,
      #workOrderContent * {
        visibility: visible !important;
      }

      .el-dialog__wrapper,
      .el-dialog,
      .el-dialog__header,
      .dialog-footer,
      .size-info {
        display: none !important;
        visibility: hidden !important;
      }

      #workOrderContent {
        position: static !important;
        display: block !important;
        width: 200mm !important; /* A5横版内容宽度（210mm - 10mm边距） */
        height: 138mm !important; /* A5横版内容高度（148mm - 10mm边距） */
        max-width: 200mm !important;
        max-height: 138mm !important;
        margin: 0 !important;
        padding: 5mm !important;
        background: white !important;
        font-family: "Microsoft YaHei", "SimSun", serif !important;
        color: black !important;
        font-size: 6px !important;
        line-height: 1.0 !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
      }

      .work-order-header {
        margin-bottom: 2mm !important;
        padding-bottom: 1mm !important;
        border-bottom: 1px solid #000 !important;
        text-align: center !important;
      }

      .company-name {
        font-weight: bold !important;
        margin: 0 0 1mm 0 !important;
        font-size: 8px !important;
      }

      .document-title {
        font-weight: bold !important;
        margin: 0 !important;
        font-size: 6px !important;
      }

      .basic-info-with-qr {
        display: flex !important;
        margin-bottom: 2mm !important;
        gap: 8px !important;
        align-items: flex-start !important;
      }

      .basic-info {
        flex: 1 !important;
        margin-bottom: 1mm !important;
      }

      .info-row {
        margin-bottom: 1mm !important;
      }

      .info-item {
        display: inline-block !important;
        margin-right: 5mm !important;
        vertical-align: top !important;
      }

      .info-item.full-width {
        display: block !important;
        margin-right: 0 !important;
        margin-bottom: 1mm !important;
      }

      .label {
        font-weight: bold !important;
        margin-right: 1mm !important;
      }

      .value {
        color: #000 !important;
      }

      .order-no {
        font-weight: bold !important;
        color: #000 !important;
      }

      .qr-code-container {
        flex-shrink: 0 !important;
        width: 12mm !important;
        height: 12mm !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      canvas {
        width: 12mm !important;
        height: 12mm !important;
        border: 1px solid #000 !important;
        display: block !important;
      }

      .production-table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin-top: 2mm !important;
      }

      .production-table th,
      .production-table td {
        border: 1px solid #000 !important;
        padding: 0.5mm !important;
        text-align: center !important;
        vertical-align: top !important;
        line-height: 1.0 !important;
        font-size: 5px !important;
      }

      .production-table th {
        background-color: #f0f0f0 !important;
        font-weight: bold !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
      }

      .project-cell,
      .craft-cell {
        text-align: left !important;
        font-size: 4px !important;
      }

      .production-table th:nth-child(1),
      .production-table td:nth-child(1) { width: 8%; }
      .production-table th:nth-child(2),
      .production-table td:nth-child(2) { width: 10%; }
      .production-table th:nth-child(3),
      .production-table td:nth-child(3) { width: 15%; }
      .production-table th:nth-child(4),
      .production-table td:nth-child(4) { width: 20%; }
      .production-table th:nth-child(5),
      .production-table td:nth-child(5) { width: 18%; }
      .production-table th:nth-child(6),
      .production-table td:nth-child(6) { width: 6%; }
      .production-table th:nth-child(7),
      .production-table td:nth-child(7) { width: 8%; }
      .production-table th:nth-child(8),
      .production-table td:nth-child(8) { width: 8%; }
      .production-table th:nth-child(9),
      .production-table td:nth-child(9) { width: 7%; }
    }
  `
  document.head.appendChild(style)
}

// 移除打印样式
const removePrintStyles = () => {
  const style = document.getElementById('simple-print-styles')
  if (style) {
    document.head.removeChild(style)
  }
}
</script>

<style lang="scss" scoped>
.work-order-dialog {
  :deep(.el-dialog__body) {
    padding: 10px;
  }
}

.size-selector {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.work-order-container {
  background: white;
  font-family: "Microsoft YaHei", "SimSun", serif;
  margin: 0 auto;
  border: 1px solid #ddd;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  position: relative;

  /* A5横版尺寸 (210mm x 148mm = 1.42:1) */
  &.size-a5 {
    width: 595px;  /* 模拟210mm */
    height: 420px; /* 模拟148mm */
    padding: 12px;
    font-size: 9px;
    line-height: 1.2;
  }

  /* A4尺寸 (210mm x 297mm = 1:1.41) */
  &.size-a4 {
    width: 595px;  /* 模拟210mm */
    height: 842px; /* 模拟297mm */
    padding: 20px;
    font-size: 12px;
    line-height: 1.4;
  }
}

.work-order-header {
  text-align: center;
  padding-bottom: 6px;
  border-bottom: 1px solid #409eff;

  .size-a5 & {
    margin-bottom: 10px;
  }

  .size-a4 & {
    margin-bottom: 15px;
  }
}

.header-content {
  .company-name {
    font-weight: bold;
    color: #303133;
    margin: 0 0 3px 0;

    .size-a5 & {
      font-size: 14px;
    }

    .size-a4 & {
      font-size: 18px;
    }
  }

  .document-title {
    font-weight: bold;
    color: #606266;
    margin: 0;

    .size-a5 & {
      font-size: 10px;
    }

    .size-a4 & {
      font-size: 12px;
    }
  }
}

.basic-info-with-qr {
  display: flex;
  gap: 10px;

  .size-a5 & {
    margin-bottom: 12px;
  }

  .size-a4 & {
    margin-bottom: 18px;
    gap: 15px;
  }
}

.basic-info {
  flex: 1;

  .info-row {
    display: flex;

    .size-a5 & {
      margin-bottom: 4px;
    }

    .size-a4 & {
      margin-bottom: 6px;
    }

    .info-item {
      display: flex;
      align-items: flex-start;

      .size-a5 & {
        margin-right: 15px;
      }

      .size-a4 & {
        margin-right: 25px;
      }

      &.full-width {
        flex: 1;
        margin-right: 0;
      }

      &.customer-require {
        align-items: flex-start;

        .value {
          white-space: pre-wrap;
          word-wrap: break-word;
          line-height: 1.3;

          .size-a5 & {
            font-size: 9px;
          }

          .size-a4 & {
            font-size: 11px;
          }
        }
      }

      .label {
        font-weight: bold;
        color: #606266;
        flex-shrink: 0;

        .size-a5 & {
          min-width: 50px;
          margin-right: 4px;
          font-size: 8px;
        }

        .size-a4 & {
          min-width: 60px;
          margin-right: 5px;
          font-size: 11px;
        }
      }

      .value {
        color: #303133;
        flex: 1;

        .size-a5 & {
          font-size: 8px;
        }

        .size-a4 & {
          font-size: 11px;
        }
      }

      // A5横版客户要求文本样式
      .customer-require-text {
        white-space: pre-wrap;
        word-wrap: break-word;
        line-height: 1.2;
        font-size: 8px;
      }

      // 订单号加粗加黑样式
      .order-no {
        font-weight: bold !important;
        color: #000 !important;
      }
    }
  }
}

.qr-code-container {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;

  .size-a5 & {
    padding-top: 5px;
  }

  .size-a4 & {
    padding-top: 8px;
  }
}

.content-table {
  .production-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed; /* 固定表格布局以控制列宽 */

    th, td {
      border: 1px solid #dcdfe6;
      text-align: center;
      vertical-align: top;
      word-wrap: break-word;
      overflow-wrap: break-word;

      .size-a5 & {
        padding: 2px 1px;
      }

      .size-a4 & {
        padding: 4px 2px;
      }
    }

    th {
      background-color: #f5f7fa;
      font-weight: bold;
      color: #303133;

      .size-a5 & {
        font-size: 7px;
      }

      .size-a4 & {
        font-size: 9px;
      }
    }

    td {
      color: #606266;

      .size-a5 & {
        font-size: 7px;
      }

      .size-a4 & {
        font-size: 9px;
      }
    }

    .project-cell, .craft-cell {
      text-align: left;

      .size-a5 & {
        font-size: 6px;
        line-height: 1.1;
      }

      .size-a4 & {
        font-size: 8px;
        line-height: 1.2;
      }
    }

    /* 列宽适配 */
    th:nth-child(1), td:nth-child(1) { width: 8%; }  /* 序号 */
    th:nth-child(2), td:nth-child(2) { width: 10%; } /* 部件 */
    th:nth-child(3), td:nth-child(3) { width: 15%; } /* 文件名 */
    th:nth-child(4), td:nth-child(4) { width: 20%; } /* 项目 */
    th:nth-child(5), td:nth-child(5) { width: 18%; } /* 拼板/工艺 */
    th:nth-child(6), td:nth-child(6) { width: 6%; }  /* P数 */
    th:nth-child(7), td:nth-child(7) { width: 8%; }  /* 份数 */
    th:nth-child(8), td:nth-child(8) { width: 8%; }  /* 纸张数 */
    th:nth-child(9), td:nth-child(9) { width: 7%; }  /* 备注 */
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 打印样式已移至动态生成的样式中 */
</style>
