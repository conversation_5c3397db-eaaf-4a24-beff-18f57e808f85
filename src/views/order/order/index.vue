<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" style="width: 100px"/>
      </el-form-item>
      <el-form-item label="下单时间" style="width: 258px">
        <el-date-picker v-model="daterangeDealDatetime" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
                        start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="客户姓名" prop="customerName">
        <el-select v-model="queryParams.customerName" placeholder="请选择客户" filterable clearable @change="handleQuery" style="width: 120px">
          <el-option v-for="item in customerList" :key="item.uuid" :label="item.customerName" :value="item.customerName" />
        </el-select>
      </el-form-item>
      <el-form-item label="下单人" prop="createBy">
        <el-select v-model="queryParams.createBy" placeholder="请选择下单人" filterable clearable @change="handleQuery" style="width: 120px">
          <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userName" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100px">
          <el-option v-for="dict in m214" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单来源" prop="orderSource">
        <el-select v-model="queryParams.orderSource" placeholder="请选择" clearable style="width: 100px">
          <el-option v-for="dict in order_source" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['order:order:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                   v-hasPermi="['order:order:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                   v-hasPermi="['order:order:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Printer" :disabled="multiple" @click="handlePrintWorkOrder">打印工单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Postcard" :disabled="multiple" @click="handlePrintSettlement">结算单</el-button>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="orderList" @selection-change="handleSelectionChange" row-key="uuid"
              :expand-row-keys="expandedRowKeys" @expand-change="handleExpandChange" :reserve-selection="true" ref="orderTable">
      <el-table-column type="expand">
        <template #default="props">
          <div class="expand-container">
            <div v-for="(item, index) in props.row.printList" :key="index" class="print-item">
              <div class="print-header">
                <div class="left">
                  <el-tag type="info" class="mr-2">
                    {{ item.printName }}
                  </el-tag>
                  <!-- <span class="text-muted ml-2">订单数量: {{ props.row.orderNum }}份</span> -->
                  <span class="text-muted ml-2">P数: {{ item.pages }}P</span>

                </div>
                <div class="right">
                  <el-button-group class="mr-3" v-if="props.row.prePressStatus !== 'Y' && item.isMakeup === 'Y'">
                    <el-button v-hasPermi="['order:order:add']" link type="primary" icon="Upload"
                               @click="handleUploadClick(item)">上传文件</el-button>
                    <el-button v-hasPermi="['order:order:add','order:order:confirmLayouts']" link type="primary"
                               icon="Position" @click="handleBoard(item)">去拼板</el-button>
                  </el-button-group>
                </div>
              </div>

              <div class="print-content">
                <div class="content-section">
                  <div class="section-icon">
                    <el-icon>
                      <Document />
                    </el-icon>
                    纸张信息
                  </div>
                  <div class="info-row">
                    <div class="info-item">
                      <span class="label">印刷纸张：</span>
                      <span class="value">{{ item.paperName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">纸张尺寸：</span>
                      <span class="value">{{ item.sizeName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">纸张克重：</span>
                      <span class="value">{{ item.weightName }}g</span>
                    </div>
                  </div>
                </div>

                <div class="content-section">
                  <div class="section-icon">
                    <el-icon>
                      <Setting />
                    </el-icon>
                    印刷参数
                  </div>
                  <div class="info-row">
                    <div class="info-item">
                      <span class="label">文件名：</span>
                      <span class="value">{{ item.originalFileName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">文件尺寸（宽*高）</span>
                      <span class="value">{{ item.paperWidth }} * {{ item.paperHeight }} mm</span>
                    </div>
                    <div class="info-item">
                      <span class="label">印刷方式：</span>
                      <span class="value">
                        <el-tag size="small" :type="item.isColorPrint === 1 ? 'success' : 'info'">
                          {{ item.isColorPrint === 1 ? '彩色' : '黑白' }}
                        </el-tag>
                        <el-tag size="small" class="ml-2" :type="item.isSinglePrint === 1 ? 'warning' : 'info'">
                          {{ item.isSinglePrint === 1 ? '单面' : '双面' }}
                        </el-tag>
                      </span>
                    </div>
                    <div class="info-item" v-if="item.isMakeup === 'Y'">
                      <span class="label">装订边缘/出血尺寸：</span>
                      <span class="value">
                        {{ item.bindingMargin === '3' ? '右边' :
                          item.bindingMargin === '4' ? '下边' :
                              item.bindingMargin === '2' ? '左边' :
                                  item.bindingMargin === '1' ? '上边' : item.bindingMargin }}
                        / {{ item.bleedMargin }}mm
                      </span>
                    </div>

                  </div>
                </div>

                <div class="content-section">
                  <div class="section-icon">
                    <el-icon>
                      <Grid />
                    </el-icon>
                    拼版信息
                  </div>

                  <div class="info-row">
                    <template v-if="item.isMakeup === 'Y'">
                      <div class="info-item">
                        <span class="label">拼版方式：</span>
                        <span class="value">{{ item.imposeTypeName }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">方案回执：</span>
                        <span class="value">
                          <template v-if="item.exManualDataResult">
                            <div v-for="(item1, index) in JSON.parse(item.exManualDataResult)" :key="index">
                              <el-tooltip :content="'任务ID: ' + item1.taskid" placement="top">
                                <el-tag :type="item1.status ? (item1.status === '200' ? 'success' : 'danger') : 'info'"
                                        size="small" style="margin-right: 8px;">
                                  {{ item1.status ? (item1.status === '200' ? '成功' : '失败') : '生成中' }}
                                </el-tag>
                              </el-tooltip>
                            </div>
                          </template>
                          <span v-else>-</span>
                        </span>
                      </div>
                      <div class="info-item">
                        <span class="label">拼版方案：</span>
                        <span class="value">
                          <div>
                            <el-tag size="small" type="success" style="margin-right: 8px;">{{
                                getMakeupInfo(item.makeupPagelayoutJson)?.ups }}拼 </el-tag>
                            <template v-if="getMakeupInfo(item.makeupPagelayoutJson)?.imposeTypes">
                              <el-tag v-for="(type, index) in getMakeupInfo(item.makeupPagelayoutJson).imposeTypes"
                                      :key="index" size="small" type="info">
                                {{ getImposeTypeLabel(type) }}
                              </el-tag>
                            </template>
                          </div>
                        </span>
                      </div>
                    </template>
                    <template v-else>
                      <div class="info-item">
                        <span class="value">不拼版</span>
                      </div>
                    </template>
                    <div class="info-item">
                      <span class="label">印刷费用：</span>
                      <span class="value price">¥{{ (item.printAmount || 0).toFixed(2) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">工艺费用：</span>
                      <span class="value price">¥{{ (item.craftTotalPrice || 0).toFixed(2) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column label="订单号" align="center" prop="orderNo" width="100">
        <template #default="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.orderNo }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="份数" align="center" prop="orderNum" width="60" sortable />
      <el-table-column label="交货时间" align="center" prop="deliveryTime" sortable>
        <template #default="scope">
          <span>{{ parseTime(scope.row.deliveryTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户" align="center" prop="customerName" sortable />
      <el-table-column label="联系人" align="center" prop="contact">
        <template #default="scope">
          <span>{{ scope.row.contact }} / {{ scope.row.phone }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="详细地址" align="center" prop="address" /> -->
      <el-table-column label="总金额" align="center" prop="orderAmount" width="100"/>
      <el-table-column label="订单来源" align="center" prop="orderSource" width="80">
        <template #default="scope">
          <dict-tag type="info" :options="order_source" :value="scope.row.orderSource" />
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priority" width="80">
        <template #default="scope">
          <dict-tag type="info" :options="m209" :value="scope.row.priority" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="m214" :value="scope.row.status" />
          <dict-tag v-if="scope.row.status === 3"
                    :options="shipping_status" :value="scope.row.shippingStatus" />
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payType">
        <template #default="scope">
          <dict-tag :options="pay_type" :value="scope.row.payType" />
        </template>
      </el-table-column>
      <el-table-column label="收款信息" align="center" width="100">
        <template #default="scope">
          <div style="display: flex; flex-direction: column; gap: 4px; align-items: center;">
            <el-tooltip v-if="scope.row.payRemark" :content="scope.row.payRemark" placement="top" effect="light">
              <div>
                <dict-tag :options="pay_status" :value="scope.row.payStatus" />
              </div>
            </el-tooltip>
            <div v-else>
              <dict-tag :options="pay_status" :value="scope.row.payStatus" />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="印前状态" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.prePressStatus === 'Y' ? 'success' : 'info'">
            {{ scope.row.prePressStatus === 'Y' ? '已完成' : '未完成' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="拼版信息" align="center">
        <template #default="scope">
          <template v-if="scope.row.printList && scope.row.printList.length > 0">
            <div v-for="(item, index) in scope.row.printList" :key="index">
              <template v-if="item.exManualDataResult">
                <div v-for="(item1, index) in JSON.parse(item.exManualDataResult)" :key="index">
                  <el-tooltip :content="'任务ID: ' + item1.taskid" placement="top">
                    <el-tag :type="item1.status ? (item1.status === '200' ? 'success' : 'danger') : 'info'" size="small"
                            style="margin-bottom: 4px;">
                      {{ item1.status ? (item1.status === '200' ? '成功' : '失败') : '生成中' }}
                    </el-tag>
                  </el-tooltip>
                </div>
              </template>
              <span v-else>-</span>
            </div>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="客户要求" align="center" prop="customerRequire" :show-overflow-tooltip="true" />
      <el-table-column label="下单人" align="center" prop="createByNiceName" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width">
        <template #default="scope">
          <div v-if="scope.row.status !== 1">
            <el-button v-hasPermi="['order:order:updatePrePressStatus']" v-if="scope.row.prePressStatus !== 'Y'" link
                       type="primary" icon="Finished" @click="handlePrePressComplete(scope.row)">印前完成</el-button>
            <el-button v-hasPermi="['order:order:updatePayStatus']" v-if="scope.row.payStatus === 'unpaid'" link
                       type="primary" icon="Money" @click="handlePayClick(scope.row)">收款</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                @pagination="getList" />


    <!-- 添加上传对话框 -->
    <UploadDialog v-model="uploadDialogVisible" :token="qiniuToken" :config="qiniuConfig" :host="qiniuHost"
                  :bucket="qiniuBucket" :file-path-prefix="qiniuFilePathPrefix" :current-item="currentUploadItem"
                  @confirm="handleUploadConfirm" />

    <!-- 添加收款确认对话框 -->
    <el-dialog :title="'收款确认'" v-model="payDialogVisible" width="500px" append-to-body>
      <el-form ref="payFormRef" :model="payForm" :rules="payRules" label-width="80px">
        <el-form-item label="支付方式" prop="payType">
          <el-select v-model="payForm.payType" placeholder="请选择支付方式" clearable style="width: 100%" value-key="value">
            <el-option v-for="dict in pay_type" :key="dict.value" :label="dict.label" :value="dict.value">
              <span>{{ dict.label }}</span>
              <span v-if="dict.value === 'balance'" style="float: right; color: #8492a6; font-size: 13px">
                余额：{{ customerBalance }} 元
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收款备注" prop="payRemark">
          <el-input v-model="payForm.payRemark" type="textarea" rows="4" show-word-limit :maxlength="150"
                    placeholder="请输入收款备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPayForm">确 定</el-button>
          <el-button @click="cancelPay">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 使用新的订单详情组件 -->
    <OrderDetail v-model="detailVisible" :order-uuid="currentOrderUuid" :title="'订单详情'" />
    <!-- 共享文件上传组件 -->
    <SharedFolderDialog
        v-model="sharedDialogVisible"
        :single="true"
        title="选择共享文件（单文件模式）"
        @replace="handleSharedFileSelect"
    />
  </div>
</template>

<script setup name="Order">
import { listOrder, getOrder, delOrder, orderSettlement, orderFlowExport, updatePrePressStatusByUuid, updatePayStatus } from "@/api/order/order";
import { Document, Setting, Grid, Money, Printer, Edit, Operation } from '@element-plus/icons-vue'
import { getQiniuToken } from "@/api/system/qiniu";
import { updateOrderPrintDetail } from "@/api/order/OrderPrintDetail";
import UploadDialog from '@/views/order/order/components/UploadDialog.vue';
import { confirmLayouts } from "@/api/order/order";
import { getCustomerAccountByCustomerUuid } from "@/api/business/CustomerAccount";
import OrderDetail from './components/orderDetailDrawer.vue'
import { listCustomer } from "@/api/business/customer";
import { getAllUserList } from "@/api/system/user";
import SharedFolderDialog from './components/SharedFolderDialog.vue';
import { getBusinessUploadType } from "@/api/order/order";
import { useRuoyiListPageState } from '@/composables/useRuoyiPageState';

const { proxy } = getCurrentInstance();
const { m209,m214,pay_status,pay_type,m212,shipping_status,order_source } = proxy.useDict( 'm209','m214','pay_status','pay_type','m212','shipping_status','order_source');

const orderList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const daterangeDealDatetime = ref([]);
const customerList = ref([]); // 添加客户列表数据
const userList = ref([]); // 添加用户列表数据

// 添加展开行状态管理
const expandedRowKeys = ref([]);

// 处理展开行变化
const handleExpandChange = (row, expanded) => {
  const key = row.uuid;
  if (expanded) {
    if (!expandedRowKeys.value.includes(key)) {
      expandedRowKeys.value.push(key);
    }
  } else {
    const index = expandedRowKeys.value.indexOf(key);
    if (index !== -1) {
      expandedRowKeys.value.splice(index, 1);
    }
  }
};

// 清理不存在的展开行
const cleanInvalidExpandedRows = () => {
  const validUuids = new Set(orderList.value.map(order => order.uuid));
  expandedRowKeys.value = expandedRowKeys.value.filter(uuid => validUuids.has(uuid));
};

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    dealDatetime: null,
    customerName: null,
    status: null,
    createBy: null,
  },
  rules: {
    orderNo: [ { required: true, message: "订单号不能为空", trigger: "blur" } ],
    customerUuid: [ { required: true, message: "客户UUID不能为空", trigger: "blur" } ],
    customerName: [ { required: true, message: "客户姓名不能为空", trigger: "blur" } ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// 添加七牛云相关状态
const qiniuToken = ref('');
const qiniuConfig = ref({
  region: 'z0',
  useCdnDomain: true
});
const qiniuHost = ref('');
const qiniuBucket = ref('');
const qiniuFilePathPrefix = ref('');

// 添加上传对话框相关状态
const uploadDialogVisible = ref(false);
const currentUploadItem = ref(null);

// 添加轮询相关的状态
const pollingTimer = ref(null);
const POLLING_INTERVAL = 40000; // 40秒轮询一次

// 添加收款相关的状态
const payDialogVisible = ref(false);
const customerBalance = ref(0);
const payForm = ref({
  uuid: '',
  payType: '',  // 确保默认值为空字符串
  payRemark: ''
});
const payRules = {
  payType: [
    { required: true, message: "请选择支付方式", trigger: "change" },
    { type: 'string', trigger: 'change' }  // 确保值是字符串类型
  ],
};

// 添加详情相关的数据
const detailVisible = ref(false);
const currentOrderUuid = ref('');

// 新增上传类型和共享文件选择状态
const uploadType = ref('');
const sharedDialogVisible = ref(false);
const sharedFileItem = ref(null);

/** 查询客户订单列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != daterangeDealDatetime && '' != daterangeDealDatetime) {
    queryParams.value.params["beginDealDatetime"] = daterangeDealDatetime.value[0];
    queryParams.value.params["endDealDatetime"] = daterangeDealDatetime.value[1];
  }
  listOrder(queryParams.value).then(response => {
    orderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
    // 清理不存在的展开行
    cleanInvalidExpandedRows();
    // 重置表格选择状态
    if (proxy.$refs.orderTable) {
      proxy.$refs.orderTable.clearSelection();
    }
  });
}


// 表单重置
function reset() {
  form.value = {
    uuid: null,
    orderNo: null,
    orderNum: null,
    dealDatetime: null,
    customerUuid: null,
    customerName: null,
    contact: null,
    phone: null,
    addressUuid: null,
    address: null,
    amount: null,
    orderAmount: null,
    priority: null,
    payType: null,
    payStatus: null,
    procStatus: null,
    status: null,
    payDatetime: null,
    finishedSizeUuid: null,
    finishedSizeName: null,
    remark: null,
    isDel: null,
    createTime: null,
    createBy: null,
    updateTime: null,
    updateBy: null
  };
  proxy.resetForm("orderRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDealDatetime.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.uuid);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  proxy.$router.push('/order/order/add');
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const uuid = row?.uuid || ids.value[0];

  // Ensure uuid is available
  if (!uuid) {
    proxy.$modal.msgError("请选择一个订单进行修改。");
    return;
  }

  const orderToUpdate = orderList.value.find(item => item.uuid === uuid);

  if (!orderToUpdate) {
    proxy.$modal.msgError("订单信息未找到，无法修改。");
    return;
  }

  // 1. 无效订单，不能修改。 (Assuming status 0 is the only modifiable state)
  if (orderToUpdate.status !== 0) {
    proxy.$modal.msgError("非待生产订单，不能修改。");
    return;
  }

  proxy.$tab.closePage({ path: '/order/order/edit' }); // Changed comma to semicolon
  proxy.$router.push(`/order/order/edit/${uuid}`);

}
/** 打印结算单按钮操作 */
async function handlePrintSettlement(row) {
  try {
    // 获取选中的UUID数组或单个UUID
    const uuids = row?.uuid ? [row.uuid] : ids.value;
    if (!uuids || uuids.length === 0) {
      proxy.$modal.msgError('请选择需要导出的结算单');
      return;
    }
    // 显示导出进度提示
    proxy.$modal.msgSuccess(`开始导出${uuids.length}个结算...`);
    // 循环导出每个订单
    for (const uuid of uuids) {
      try {
        const response = await orderSettlement(uuid);
        // 创建Blob对象
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });
        // 获取订单信息用于文件名
        const orderInfo = orderList.value.find(item => item.uuid === uuid);
        const fileName = `${orderInfo.orderNo}_${orderInfo.customerName}_结算单.docx`;
        // 创建下载链接并触发下载
        const downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(blob);
        downloadLink.download = fileName;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        // 清理
        document.body.removeChild(downloadLink);
        window.URL.revokeObjectURL(downloadLink.href);
        // 短暂延迟，避免浏览器同时触发太多下载
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`导出工单失败 (UUID: ${uuid}):`, error);
        proxy.$modal.msgError(`导出工单失败 (订单号: ${orderList.value.find(item => item.uuid === uuid)?.orderNo})`);
      }
    }
    proxy.$modal.msgSuccess('结算单导出完成');
  } catch (error) {
    console.error('批量导出结算单失败:', error);
    proxy.$modal.msgError('批量导出结算单失败');
  }
}

/** 打印工单按钮操作 */
async function handlePrintWorkOrder(row) {
  try {
    // 获取选中的UUID数组或单个UUID
    const uuids = row?.uuid ? [row.uuid] : ids.value;
    if (!uuids || uuids.length === 0) {
      proxy.$modal.msgError('请选择需要打印的订单');
      return;
    }
    // 显示处理进度提示
    proxy.$modal.msgSuccess(`开始打开${uuids.length}个工单...`);
    // 循环处理每个订单
    for (const uuid of uuids) {
      try {
        const response = await orderFlowExport(uuid);
        // 创建Blob对象
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });
        // 获取订单信息用于文件名
        const orderInfo = orderList.value.find(item => item.uuid === uuid);
        const fileName = `${orderInfo.orderNo}_${orderInfo.customerName}_工单.docx`;

        // 创建URL并在新窗口中打开
        const fileUrl = window.URL.createObjectURL(blob);
        const newWindow = window.open('', '_blank');

        if (newWindow) {
          // 构建HTML内容
          const htmlContent = [
            '<!DOCTYPE html>',
            '<html>',
            '<head>',
            `<title>${fileName}</title>`,
            '<style>',
            'body { font-family: Arial, sans-serif; margin: 20px; text-align: center; }',
            '.container { max-width: 600px; margin: 0 auto; padding: 20px; }',
            '.file-info { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }',
            '.buttons { margin: 20px 0; }',
            '.btn { display: inline-block; padding: 10px 20px; margin: 0 10px; background: #409eff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; font-size: 14px; }',
            '.btn:hover { background: #337ecc; }',
            '.btn-download { background: #67c23a; }',
            '.btn-download:hover { background: #529b2e; }',
            '.instructions { background: #fff6e6; border: 1px solid #ffd666; padding: 15px; border-radius: 4px; margin: 20px 0; text-align: left; }',
            '</style>',
            '</head>',
            '<body>',
            '<div class="container">',
            '<h2>工单打印</h2>',
            '<div class="file-info">',
            `<h3>${fileName}</h3>`,
            `<p>订单号: ${orderInfo.orderNo}</p>`,
            `<p>客户: ${orderInfo.customerName}</p>`,
            '</div>',
            '<div class="instructions">',
            '<h4>打印说明：</h4>',
            '<ol>',
            '<li>点击下方"下载文件"按钮下载工单文档</li>',
            '<li>使用 Microsoft Word 或 WPS 等软件打开文档</li>',
            '<li>在软件中使用打印功能进行打印</li>',
            '</ol>',
            '</div>',
            '<div class="buttons">',
            `<a href="${fileUrl}" download="${fileName}" class="btn btn-download">下载文件</a>`,
            '<button onclick="window.close()" class="btn">关闭窗口</button>',
            '</div>',
            '</div>',
            '</body>',
            '</html>'
          ].join('\n');

          // 写入HTML内容
          newWindow.document.write(htmlContent);
          newWindow.document.close();

          // 在新窗口中添加JavaScript功能
          setTimeout(() => {
            if (newWindow && !newWindow.closed) {
              const script = newWindow.document.createElement('script');
              script.textContent = `
                // 自动触发下载
                setTimeout(function() {
                  const link = document.querySelector('a[download]');
                  if (link) {
                    link.click();
                  }
                }, 1000);

                // 清理资源
                window.addEventListener('beforeunload', function() {
                  if (window.opener) {
                    window.opener.URL.revokeObjectURL('${fileUrl}');
                  }
                });
              `;
              newWindow.document.head.appendChild(script);
            }
          }, 100);
        } else {
          // 如果无法打开新窗口，回退到下载模式
          proxy.$modal.msgWarning('无法打开新窗口，将直接下载文件');
          const downloadLink = document.createElement('a');
          downloadLink.href = fileUrl;
          downloadLink.download = fileName;
          document.body.appendChild(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);
        }

        // 短暂延迟，避免浏览器同时处理太多窗口
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 延迟清理URL（给新窗口足够时间使用）
        setTimeout(() => {
          window.URL.revokeObjectURL(fileUrl);
        }, 30000);

      } catch (error) {
        console.error(`处理工单失败 (UUID: ${uuid}):`, error);
        proxy.$modal.msgError(`处理工单失败 (订单号: ${orderList.value.find(item => item.uuid === uuid)?.orderNo})`);
      }
    }
    proxy.$modal.msgSuccess('工单处理完成');
  } catch (error) {
    console.error('批量处理工单失败:', error);
    proxy.$modal.msgError('批量处理工单失败');
  }
}



/** 删除按钮操作 */
function handleDelete(row) {
  const _uuids = row.uuid || ids.value;
  proxy.$modal.confirm('是否确认删除客户订单编号为"' + _uuids + '"的数据项？').then(function() {
    return delOrder(_uuids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('order/order/export', {
    ...queryParams.value
  }, `order_${new Date().getTime()}.xlsx`)
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  currentOrderUuid.value = row.uuid;
  detailVisible.value = true;
}


// 获取七牛云配置
const getQiniuConfig = async () => {
  try {
    const res = await getQiniuToken();
    if (res.code === 200) {
      qiniuToken.value = res.data.upToken;
      qiniuHost.value = res.data.host;
      qiniuBucket.value = res.data.bucket;
      qiniuFilePathPrefix.value = res.data.filePathPrefix;
    } else {
      throw new Error(res.msg || '获取上传配置失败');
    }
  } catch (error) {
    console.error('获取七牛云配置失败:', error);
    proxy.$modal.msgError(error.message || "获取七牛云配置失败");
  }
};

// 获取客户列表
const getCustomerList = async () => {
  try {
    const res = await listCustomer({ pageSize: 9999 });
    if (res.code === 200) {
      customerList.value = res.rows;
    }
  } catch (error) {
    console.error('获取客户列表失败:', error);
    proxy.$modal.msgError('获取客户列表失败');
  }
};

// 获取用户列表
const getUserList = async () => {
  try {
    const res = await getAllUserList();
    if (res.code === 200) {
      userList.value = res.data;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    proxy.$modal.msgError('获取用户列表失败');
  }
};

// 获取上传类型
const getUploadType = async () => {
  try {
    const res = await getBusinessUploadType();
    if (res.code === 200) {
      uploadType.value = res.data.userType;
    }
  } catch (e) {
    uploadType.value = '';
  }
};

// 修改处理上传按钮点击函数
const handleUploadClick = (item) => {
  if (uploadType.value === 'share') {
    sharedFileItem.value = item;
    sharedDialogVisible.value = true;
  } else {
    // 重置当前上传项
    currentUploadItem.value = {
      ...item,
      printDetail: {
        ...item,
        fileUrl: null,
        originalFileName: null,
        custFileUuid: null,
        fileUuid: null,
        taskUuid: item.uuid // 保存任务ID
      }
    };
    uploadDialogVisible.value = true;
  }
};

// 修改处理上传确认函数
const handleUploadConfirm = async (uploadedData) => {
  try {
    // 检查页数是否一致
    const currentPages = currentUploadItem.value?.pages;
    const uploadedPages = uploadedData.pages;

    if (currentPages && uploadedPages && currentPages !== uploadedPages) {
      // 如果页数不一致，显示确认对话框
      try {
        await proxy.$modal.confirm(
            `上传文件的页数(${uploadedPages}页)与当前记录的页数(${currentPages}页)不一致，是否继续？`,
            '页数不一致提醒'
        );
      } catch (e) {
        // 用户点击取消，终止上传
        return;
      }
    }

    // 更新打印明细
    const updateData = {
      uuid: currentUploadItem.value.printDetail.taskUuid, // 使用保存的任务ID
      custFileUuid: uploadedData.custFileUuid,
      fileUuid: uploadedData.fileUuid,
      fileUrl: Array.isArray(uploadedData.fileUrl) ? uploadedData.fileUrl[0] : uploadedData.fileUrl,
      originalFileName: uploadedData.originalFileName,
    };

    const res = await updateOrderPrintDetail(updateData);
    if (res.code === 200) {
      // 关闭上传对话框
      uploadDialogVisible.value = false;
      // 显示成功消息
      proxy.$modal.msgSuccess("上传成功");
      // 立即刷新列表
      await getList();
    } else {
      throw new Error(res.msg || '更新失败');
    }
  } catch (error) {
    console.error('更新失败:', error);
    proxy.$modal.msgError(error.message || "更新失败");
  }
};

// 添加获取拼版类型中文名称的方法
const getImposeTypeLabel = (type) => {
  switch (type) {
    case 'CNS':
      return '裁切堆叠';
    case 'SNR':
      return '重复拼板';
    case 'NS':
      return '顺序拼板';
    default:
      return type;
  }
};

// 修改获取拼版信息的方法，获取所有的 imposetype
const getMakeupInfo = (makeupPagelayoutJson) => {
  if (!makeupPagelayoutJson) return null;
  try {
    const makeupInfo = JSON.parse(makeupPagelayoutJson);
    // 获取所有不重复的 imposetype
    const imposeTypes = [...new Set(makeupInfo.layouts?.map(layout => layout.imposetype) || [])];
    return {
      ups: makeupInfo.ups,
      imposeTypes // 返回所有的 imposetype 数组
    };
  } catch (e) {
    console.error('解析拼版信息失败:', e);
    return null;
  }
};

// 处理去拼板
const handleBoard = async (item) => {
  try {
    // 验证文件是否存在
    if (!item.fileUrl) {
      proxy.$modal.msgError("请先上传文件后再去拼板");
      return;
    }
    // 添加确认对话框
    try {
      await proxy.$modal.confirm('是否确认执行拼板操作？执行拼板操作后才可以发送打印任务。', "提示");
    } catch (e) {
      // 用户点击取消，终止操作
      return;
    }

    // 调用发送打印的API，传入完整的数据对象
    var data = {taskUuid:item.uuid}
    const response = await confirmLayouts(data);

    if (response.code === 200) {
      proxy.$modal.msgSuccess("发送拼板成功");
      // 刷新列表
      getList();
    } else {
      throw new Error(response.msg || '发送拼板失败');
    }

  } catch (error) {
    console.error('拼板操作失败:', error);
    proxy.$modal.msgError(error.message || "拼板操作失败");
  }
};

// 处理收款按钮点击
const handlePayClick = async (row) => {
  payForm.value = {
    uuid: row.uuid,
    payType: '',  // 始终初始化为空字符串
    payRemark: row.payRemark || ''
  };

  // 获取客户账户余额
  try {
    const res = await getCustomerAccountByCustomerUuid(row.customerUuid);
    if (res.code === 200 && res.data) {
      customerBalance.value = res.data.balance || 0;
    }
  } catch (error) {
    console.error('获取客户账户余额失败:', error);
    customerBalance.value = 0;
  }

  payDialogVisible.value = true;
};

// 取消收款
const cancelPay = () => {
  payDialogVisible.value = false;
  customerBalance.value = 0;
  payForm.value = {
    uuid: '',
    payType: '',  // 确保重置时也设置为空字符串
    payRemark: ''
  };
};

// 提交收款表单
const submitPayForm = () => {
  proxy.$refs["payFormRef"].validate(valid => {
    if (valid) {
      const payData = {
        ...payForm.value,
        payStatus: 'paid' // 收款状态。paid=已收款，unpaid=未收款
      };
      handlePayStatus(payData);
      payDialogVisible.value = false;
    }
  });
};

// 收款状态处理
const handlePayStatus = (data) => {
  proxy.$modal.confirm('是否确认完成收款操作？').then(function() {
    return updatePayStatus(data);
  }).then(() => {
    proxy.$modal.msgSuccess("收款成功");
    getList();
  }).catch(() => {});
};

// 添加印前完成处理函数
const handlePrePressComplete = (row) => {
  // Validate makeup tasks before proceeding
  if (row.printList && row.printList.length > 0) {
    for (const printItem of row.printList) {
      if (printItem.isMakeup === 'Y') {
        if (!printItem.exManualDataResult) {
          proxy.$modal.msgError(`印刷品 '${printItem.printName || '未命名'}' 需要拼版，但无拼版结果数据，无法完成印前。`);
          return;
        }
        try {
          const parsedResults = JSON.parse(printItem.exManualDataResult);
          if (!parsedResults || parsedResults.length === 0) {
            proxy.$modal.msgError(`印刷品 '${printItem.printName || '未命名'}' 无有效的拼版任务回执，无法完成印前。`);
            return;
          }
          for (const taskResult of parsedResults) {
            if (taskResult.status !== '200') {
              proxy.$modal.msgError(`印刷品 '${printItem.printName || '未命名'}' 的拼版任务 (ID: ${taskResult.taskid}) 未成功 ，无法完成印前。`);
              return;
            }
          }
        } catch (e) {
          proxy.$modal.msgError(`印刷品 '${printItem.printName || '未命名'}' 拼版结果解析失败，无法完成印前。`);
          console.error("Error parsing exManualDataResult for printItem:", printItem, e);
          return;
        }
      }
    }
  }

  proxy.$modal.confirm('是否确认完成印前工作？').then(function() {
    const data = {
      uuid: row.uuid,
      prePressStatus: 'Y', // 印前完成
      status: '2'//订单状态：制作中
    };
    return updatePrePressStatusByUuid(data);
  }).then(() => {
    proxy.$modal.msgSuccess("印前工作已完成");
    getList();
  }).catch(() => {});
};

// 获取单个订单的状态更新
const getOrderStatus = async (orderUuid) => {
  try {
    const response = await getOrder(orderUuid);
    if (response.code === 200) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error('获取订单状态失败:', error);
    return null;
  }
};

// 更新单个订单的状态
const updateOrderStatus = (updatedOrder) => {
  const index = orderList.value.findIndex(item => item.uuid === updatedOrder.uuid);
  if (index !== -1) {
    // 只更新需要实时更新的字段
    orderList.value[index] = {
      ...orderList.value[index],
      exManualDataResult: updatedOrder.exManualDataResult
    };
  }
};

// 开始轮询
const startPolling = () => {
  if (pollingTimer.value) return;

  pollingTimer.value = setInterval(async () => {
    // 获取所有有拼版任务的订单
    const ordersWithManualTasks = orderList.value.filter(order =>
        order.printList?.some(print =>
            // 只要有拼版任务的订单都需要轮询更新
            print.isMakeup === 'Y'
        )
    );

    if (ordersWithManualTasks.length === 0) return;

    try {
      // 使用与初始查询相同的参数，但包含需要更新的订单的UUID
      const queryParams = {
        pageNum: 1,
        pageSize: ordersWithManualTasks.length,
        orderUuids: ordersWithManualTasks.map(order => order.uuid)
      };

      const response = await listOrder(queryParams);

      if (response.code === 200 && response.rows) {
        // 更新每个返回的订单数据
        response.rows.forEach(updatedOrder => {
          const index = orderList.value.findIndex(item => item.uuid === updatedOrder.uuid);
          if (index !== -1) {
            // 更新整个订单对象，保持其他属性不变
            orderList.value[index] = {
              ...orderList.value[index],
              ...updatedOrder
            };
          }
        });
      }
    } catch (error) {
      console.error('轮询更新订单状态失败:', error);
    }
  }, POLLING_INTERVAL);
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// 处理共享文件选择（替换模式，只能选择一个文件）
const handleSharedFileSelect = async (file) => {
  // file: { id, label, fullPath }
  const updateData = {
    uuid: sharedFileItem.value.uuid,
    custFileUuid: file.id,
    fileUrl: file.fullPath,
    originalFileName: file.label,
  };
  try {
    const res = await updateOrderPrintDetail(updateData);
    if (res.code === 200) {
      sharedDialogVisible.value = false;
      proxy.$modal.msgSuccess("文件已替换并上传成功");
      await getList();
    } else {
      throw new Error(res.msg || '更新失败');
    }
  } catch (error) {
    proxy.$modal.msgError(error.message || "更新失败");
  }
};

// 使用若依专用页面状态管理
const {
  pageState,
  refreshPage,
  listState,
  handlePageChange,
  handleSizeChange,
  handleSearch,
  resetSearch,
  setListData,
  setLoading,
  msgSuccess,
  msgError,
  confirm
} = useRuoyiListPageState(async () => {
  await getList();
  await getQiniuConfig();
  await getCustomerList();
  await getUserList();
  await getUploadType();
  startPolling();
}, {
  debug: import.meta.env.DEV,
  reinitOnActivated: false,
  autoPagination: true,
  defaultPageSize: 10,
  autoSearch: true
});

// 在 onUnmounted 中添加停止轮询
onUnmounted(() => {
  stopPolling(); // 添加停止轮询
});
</script>

<style lang="scss" scoped>
.expand-container {
  padding: 0 20px;

  .print-item {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .print-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ebeef5;

      .left {
        display: flex;
        align-items: center;
        gap: 8px;

        .text-muted {
          color: #909399;
          font-size: 14px;
        }
      }

      .right {
        display: flex;
        align-items: center;

        .total-price {
          color: #f56c6c;
          font-size: 15px;
          font-weight: 500;
        }
      }
    }

    .print-content {
      display: flex;
      gap: 40px;

      .content-section {
        flex: 1;

        .section-icon {
          display: flex;
          align-items: center;
          color: #606266;
          margin-bottom: 12px;

          .el-icon {
            margin-right: 4px;
            font-size: 16px;
          }
        }

        .info-row {
          .info-item {
            margin-bottom: 8px;
            display: flex;
            align-items: center;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: #909399;
              margin-right: 8px;
              min-width: 70px;
            }

            .value {
              color: #303133;

              &.price {
                color: #f56c6c;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}

.mr-2 {
  margin-right: 8px;
}

.mr-3 {
  margin-right: 12px;
}

.ml-2 {
  margin-left: 8px;
}
</style>

