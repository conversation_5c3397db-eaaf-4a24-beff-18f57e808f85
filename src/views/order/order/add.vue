<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">新增订单</span>
        </div>
      </template>

      <!-- 基本信息部分 -->
      <el-form ref="orderRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户" prop="customerUuid">
              <el-popover v-if="selectedCustomer?.discountCraftList?.length" placement="right" :width="300"
                trigger="hover">
                <template #reference>
                  <el-select v-model="form.customerUuid" placeholder="请选择客户" filterable clearable
                    @change="handleCustomerChange" style="width: 100%">
                    <el-option v-for="item in customerList" :key="item.uuid" :label="item.customerName"
                      :value="item.uuid" />
                  </el-select>
                </template>
                <div class="discount-info-content">
                  <h4 style="margin: 0 0 10px 0"> {{ selectedCustomer?.levelName }}</h4>
                  <el-table :data="selectedCustomer.discountCraftList" border size="small" style="width: 100%">
                    <el-table-column label="工艺种类" align="center" width="90">
                      <template #default="scope">
                        <el-tag size="small" :type="scope.row.craftClassType === '9999' ? 'success' : 'warning'"
                          :effect="isMatchingDiscount(scope.row) ? 'light' : 'plain'">
                          {{ getCraftTypeName(scope.row.craftClassType) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="数量范围" align="center" width="110">
                      <template #default="scope">
                        <span :class="{ 'matching-discount': isMatchingDiscount(scope.row) }">
                          {{ scope.row.minQuantity }}
                          <template v-if="scope.row.maxQuantity">-{{ scope.row.maxQuantity }}</template>
                          <template v-else>份以上</template>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="折扣率" align="center" width="70">
                      <template #default="scope">
                        <el-tag size="small" :type="getDiscountRateType(scope.row.discountRate)"
                          :effect="isMatchingDiscount(scope.row) ? 'light' : 'plain'">
                          {{ (scope.row.discountRate * 100).toFixed(0) }}%
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-popover>
              <el-select v-else v-model="form.customerUuid" placeholder="请选择客户" filterable clearable
                @change="handleCustomerChange" style="width: 100%">
                <el-option v-for="item in customerList" :key="item.uuid" :label="item.customerName"
                  :value="item.uuid" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="联系人" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item label="自提" prop="isPickup">
              <el-switch v-model="form.isPickup" active-value="Y" inactive-value="N" inline-prompt active-text="是"
                inactive-text="否" @change="handlePickupChange" />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="form.isPickup === 'N'">
            <el-form-item label="配送地址" prop="addressUuid">
              <el-select v-model="form.addressUuid" placeholder="请选择配送地址" filterable clearable
                @change="handleAddressChange" style="width: 100%">
                <el-option v-for="item in addressList" :key="item.uuid" :label="item.address" :value="item.uuid" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="销售人员" prop="salesUserId">
              <el-select v-model="form.salesUserId" placeholder="请选择销售人员" filterable clearable style="width: 100%">
                <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="订单份数" prop="orderNum">
              <div class="order-num-container">
                <el-input-number v-model="form.orderNum" :model-value="form.orderNum" :min="1" :precision="0" :step="1"
                  :max="999999" controls-position="right" placeholder="请输入订单份数" style="width: 150px"
                  @update:model-value="(val) => form.orderNum = val" />
                <el-popover placement="right" :width="300" trigger="hover" v-if="discountList.length > 0">
                  <template #reference>
                    <el-button link type="primary" style="margin-left: 10px">
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                    </el-button>
                  </template>
                  <div class="discount-info">
                    <h4 style="margin: 0 0 10px 0">客户和订单份数的匹配折扣</h4>
                    <el-table :data="discountList" border size="small" style="width: 100%">
                      <el-table-column label="工艺种类" align="center" width="90">
                        <template #default="scope">
                          <el-tag size="small" :type="scope.row.craftClassType === '9999' ? 'success' : 'warning'"
                            effect="plain">
                            {{scope.row.craftClassType === '9999' ? '印刷' : m207.find(dict => dict.value ===
                            scope.row.craftClassType)?.label}}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="数量范围" align="center" width="110">
                        <template #default="scope">
                          <span>{{ scope.row.minQuantity }}</span>
                          <span v-if="scope.row.maxQuantity">-{{ scope.row.maxQuantity }}</span>
                          <span v-else>份以上</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="折扣率" align="center" width="70">
                        <template #default="scope">
                          <el-tag size="small" :type="getDiscountRateType(scope.row.discountRate)" effect="plain">
                            {{ (scope.row.discountRate * 100).toFixed(0) }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-popover>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优先级" prop="priority">
              <el-radio-group v-model="form.priority">
                <el-radio-button v-for="dict in m209" :key="dict.value" :value="parseInt(dict.value)">
                  {{ dict.label }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交货时间" prop="deliveryTime">
              <el-date-picker v-model="form.deliveryTime" type="datetime" placeholder="交货时间" :default-time="defaultTime"
                format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 文件尺寸部分 -->
        <el-row>
          <el-col :span="6">
            <el-form-item label="尺寸选择" prop="finishedSizeType">
              <el-radio-group v-model="form.finishedSizeType" @change="handleFinishedSizeTypeChange">
                <el-radio :value="1">文件尺寸</el-radio>
                <el-radio :value="2">自定义尺寸</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- 选择文件尺寸时显示 -->
          <el-col :span="6" v-if="form.finishedSizeType === 1">
            <el-form-item label="文件尺寸" prop="finishedSizeUuid">
              <el-select v-model="form.finishedSizeUuid" placeholder="请选择文件尺寸" filterable clearable
                @change="handleFinishedSizeChange" style="width: 200px">
                <el-option v-for="size in finishedSizeList" :key="size.uuid"
                  :label="`${size.sizeName} (${size.width}×${size.height}mm)`" :value="size.uuid" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 自定义尺寸时显示 -->
          <el-col :span="6" v-if="form.finishedSizeType === 2">
            <el-form-item label="文件尺寸" required>
              <div style="display: flex; flex-direction: column;">
                <div style="display: flex; align-items: center;">
                  <el-form-item prop="paperWidth" :rules="{ required: true, message: '请输入成品宽度', trigger: 'blur' }">
                    <el-input-number v-model="form.paperWidth" placeholder="成品宽" :min="1" :max="9999" :precision="2"
                      @change="handleCustomSizeChange" controls-position="right" style="width: 100px" />
                  </el-form-item>
                  <span style="margin: 0 8px;">×</span>
                  <el-form-item prop="paperHeight" :rules="{ required: true, message: '请输入成品高度', trigger: 'blur' }">
                    <el-input-number v-model="form.paperHeight" placeholder="成品高" :min="1" :max="9999" :precision="2"
                      @change="handleCustomSizeChange" controls-position="right" style="width: 100px" />
                  </el-form-item>
                </div>
                <div v-if="form.printList && form.printList.length > 0 && form.printList[0].sizeWarning"
                  class="size-warning">
                  {{ form.printList[0].sizeWarning }}
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="客户要求" prop="customerRequire">
              <el-input v-model="form.customerRequire" type="textarea" :rows="3" show-word-limit maxlength="700"
                placeholder="请输入客户要求" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 产品信息部分 -->
        <el-divider content-position="center">
          <span>产品信息</span>
        </el-divider>

        <div v-if="!selectedProduct" class="empty-product">
          <el-empty description="请选择产品">
            <el-button type="primary" @click="showProductDialog = true">选择产品</el-button>
          </el-empty>
        </div>

        <template v-else>
          <div class="product-header">
            <h3>{{ selectedProduct.productName }}</h3>
            <el-button type="primary" link @click="showProductDialog = true">更换产品</el-button>
          </div>

          <!-- 产品明细部分 -->
          <div class="product-details" v-if="form.printList && form.printList.length > 0">
            <div v-for="(item, index) in form.printList" :key="index" class="detail-item">
              <div class="item-header">
                <span class="title">{{ item.partName }}</span>
                <el-button type="danger" icon="Delete" circle size="small" class="delete-btn"
                  @click="handleDeletePart(index)" />
              </div>
              <el-row :gutter="20">
                <el-col :span="16">
                  <div class="file-upload-container">
                    <!-- 上传模式标识 -->
                    <div class="upload-mode-header">
                      <div class="mode-indicator">
                        <el-icon size="16" :color="uploadType === 'share' ? '#409EFF' : '#67C23A'">
                          <FolderOpened v-if="uploadType === 'share'" />
                          <Upload v-else />
                        </el-icon>
                        <span class="mode-text">
                          {{ uploadType === 'share' ? '共享文件夹模式' : '直接上传模式' }}
                        </span>
                      </div>
                      <el-tag
                        :type="uploadType === 'share' ? 'info' : 'success'"
                        size="small"
                        effect="light"
                        class="mode-tag"
                        :title="uploadType === 'share' && uploadPath ? uploadPath : ''"
                      >
                        <template v-if="uploadType === 'share'">
                          <el-icon size="12" style="margin-right: 4px;"><Link /></el-icon>
                          {{ uploadPath || '共享地址' }}
                        </template>
                        <template v-else>
                          上传文件
                        </template>
                      </el-tag>
                    </div>

                    <!-- 文件上传内容区域 -->
                    <div class="upload-content-area">
                      <template v-if="uploadType === 'share'">
                        <div class="shared-upload-section">
                          <div class="upload-action-area">
                            <el-button
                              type="primary"
                              @click="item.sharedDialogVisible = true"
                              class="select-file-button"
                              :icon="item.sharedFile ? 'Refresh' : 'FolderOpened'"
                            >
                              {{ item.sharedFile ? '重新选择文件' : '选择文件' }}
                            </el-button>
                          </div>

                          <!-- 已选择文件信息 -->
                          <div v-if="item.sharedFile" class="selected-file-display">
                            <div class="file-info-card">
                              <div class="file-icon">
                                <el-icon size="20" color="#409EFF">
                                  <Document />
                                </el-icon>
                              </div>
                              <div class="file-details">
                                <div class="file-name">{{ item.sharedFile.name || item.sharedFile.label }}</div>
                                <div class="file-path">{{ item.sharedFile.displayPath }}</div>
                              </div>
                              <div class="file-status">
                                <el-tag type="success" size="small" effect="light">
                                  <el-icon size="12"><Check /></el-icon>
                                  已选择
                                </el-tag>
                              </div>
                            </div>
                          </div>

                          <SharedFolderDialog
                            v-model="item.sharedDialogVisible"
                            :single="true"
                            title="选择共享文件（单文件模式）"
                            @replace="file => handleSharedFileSelect(file, index)"
                          />
                        </div>
                      </template>

                      <template v-else>
                        <div class="direct-upload-section">
                          <PdfUpload
                            :model-value="item"
                            :index="index"
                            :printList="form.printList"
                            label="文件上传"
                            :prop="`printList.${index}.custFileUuid`"
                            :token="qiniuToken"
                            :config="qiniuConfig"
                            :host="qiniuHost"
                            :bucket="qiniuBucket"
                            :filePathPrefix="qiniuFilePathPrefix"
                            @update:printList="updatePrintList"
                            @fileUploaded="handleFileUploaded"
                            @fileRemoved="handleFileRemoved"
                          />
                        </div>
                      </template>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <!-- 基础信息 -->
                <el-col :span="8">
                  <div class="basic-info">
                    <div class="section-title">基础信息</div>
                    <el-form-item label="文件尺寸">
                      <el-form-item :prop="`printList.${index}.paperWidth`"
                        :rules="{ required: true, message: '请输入文件尺寸宽度', trigger: 'change' }">
                        <el-input-number v-model="item.paperWidth" placeholder="文件宽" :min="1" :max="9999" :precision="2"
                          @change="() => handleItemSizeChange(index)" controls-position="right" style="width: 150px" />
                        <span class="unit">mm</span>
                      </el-form-item>
                      <span style="margin: 0 8px;"> </span>
                      <el-form-item :prop="`printList.${index}.paperHeight`"
                        :rules="{ required: true, message: '请输入文件尺寸', trigger: 'change' }">
                        <el-input-number v-model="item.paperHeight" placeholder="文件高" :min="1" :max="9999"
                          :precision="2" @change="() => handleItemSizeChange(index)" controls-position="right"
                          style="width: 150px" />
                        <span class="unit">mm</span>
                      </el-form-item>
                      <div v-if="item.sizeWarning" class="size-warning">
                        {{ item.sizeWarning }}
                      </div>
                    </el-form-item>

                    <el-form-item label="P数" :prop="`printList.${index}.pages`"
                      :rules="{ required: true, message: '请输入P数', trigger: 'change' }">
                      <el-input-number v-model="item.pages" placeholder="请输入P数" :min="1" :precision="0"
                        controls-position="right" style="width: 150px" :max="99999"
                        @change="(val) => handlePagesChange(index)" />
                      <span class="unit">P</span>
                    </el-form-item>

                    <el-form-item label="纸张种类" :prop="`printList.${index}.classUuid`"
                      :rules="{ required: true, message: '请选择纸张种类', trigger: 'change' }">
                      <el-select v-model="item.classUuid" placeholder="请选择纸张种类" filterable clearable
                        @change="(val) => handlePaperClassChange(val, index)" style="width: 100%">
                        <el-option v-for="cls in paperClassList" :key="cls.uuid" :label="cls.className"
                          :value="cls.uuid">
                          <template #default>
                            <span>{{ cls.className }}</span>
                            <span class="paper-count">{{ cls.paperMainCount }}</span>
                          </template>
                        </el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="印刷纸张" :prop="`printList.${index}.paperUuid`"
                      :rules="{ required: true, message: '请选择纸张', trigger: 'change' }">
                      <el-select v-model="item.paperUuid" placeholder="先选择纸张种类" filterable clearable
                        @change="(val) => handlePaperChange(val, index)" style="width: 100%">
                        <el-option v-for="paper in item.paperList" :key="paper.uuid"
                          :label="paper.paperName + (paper.hasPrinterCraft === 1 ? ' (✓)' : ' (✗)')"
                          :value="paper.uuid">
                          <template #default>
                            <span>{{ paper.paperName }}</span>
                            <span v-if="paper.hasPrinterCraft === 1" class="check-mark"> (✓)</span>
                            <span v-else> (✗)</span>
                          </template>
                        </el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="纸张尺寸" :prop="`printList.${index}.makeupSize`"
                      :rules="{ required: true, message: '纸张尺寸不能为空', trigger: 'change' }">
                      <el-input v-model="item.makeupSize" disabled placeholder="纸张尺寸将根据选择的纸张自动填充" />
                    </el-form-item>

                    <!-- 修改印刷色彩 -->
                    <el-form-item label="印刷色彩" :prop="`printList.${index}.isColorPrint`">
                      <el-radio-group v-model="item.isColorPrint" @change="() => handlePrintOptionChange(index)">
                        <el-radio v-for="dict in m204" :key="dict.value" :value="parseInt(dict.value)">
                          {{ dict.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>

                    <!-- 修改单双面 -->
                    <el-form-item label="单双面" :prop="`printList.${index}.isSinglePrint`">
                      <el-radio-group v-model="item.isSinglePrint" @change="() => handlePrintOptionChange(index)">
                        <el-radio v-for="dict in m205" :key="dict.value" :value="parseInt(dict.value)">
                          {{ dict.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>

                    <el-form-item label="纸张单价" :prop="`printList.${index}.printUnitPrice`"
                      :rules="{ required: true, message: '请输入单价', trigger: 'change' }">
                      <div class="price-info">
                        <template
                          v-if="item.originalPrintUnitPrice && item.originalPrintUnitPrice !== item.printUnitPrice">
                          <span class="original-price">{{ item.originalPrintUnitPrice?.toFixed(2) }}</span>
                          <el-tag size="small" type="success" effect="plain" class="discount-tag">
                            {{ ((item.printUnitPrice / item.originalPrintUnitPrice) * 100).toFixed(0) }}%
                          </el-tag>
                        </template>
                        <template v-else>
                          <el-tag size="small" type="info" effect="plain" class="discount-tag">
                            100%
                          </el-tag>
                        </template>
                      </div>
                      <el-input-number v-model="item.printUnitPrice" :min="0" :precision="2" :max="9999" :step="0.1"
                        controls-position="right" style="width: 100px" />
                      <span class="unit">元</span>
                    </el-form-item>
                  </div>
                </el-col>
                <!-- 拼版设置 -->
                <el-col :span="8">
                  <div class="imposition-settings">
                    <div class="section-title">拼版设置</div>
                    <!-- 修改是否拼版 -->
                    <el-form-item :label="'是否拼版'" :prop="`printList.${index}.isMakeup`">
                      <el-switch v-model="item.isMakeup" inline-prompt active-value="Y" inactive-value="N"
                        active-text="是" inactive-text="否" @change="handleImpositionChange($event, index)" />
                    </el-form-item>

                    <!-- 只在选择拼版时显示其他选项 -->
                    <template v-if="item.isMakeup === 'Y'">
                      <!-- 修改裁切标记 -->
                      <el-form-item :label="'裁切标记'" :prop="`printList.${index}.isCut`">
                        <el-switch v-model="item.isCut" inline-prompt active-value="Y" inactive-value="N"
                          active-text="是" inactive-text="否" />
                      </el-form-item>

                      <el-form-item :label="'出血尺寸'" :prop="`printList.${index}.bleedMargin`">
                        <el-input-number v-model="item.bleedMargin" :min="0" :max="10" :precision="2" :step="0.5"
                          :value-on-clear="3" />
                        <span class="unit">mm</span>
                      </el-form-item>

                      <el-form-item :label="'装订边缘'" :prop="`printList.${index}.bindingMargin`">
                        <el-radio-group v-model="item.bindingMargin" :model-value="item.bindingMargin || 2">
                          <el-radio v-for="dict in m212" :key="dict.value" :value="parseInt(dict.value)">
                            {{ dict.label === 'RIGHT' ? '右边' :
                            dict.label === 'BOTTOM' ? '下边' :
                            dict.label === 'LEFT' ? '左边' :
                            dict.label === 'TOP' ? '上边' : dict.label }}
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <!-- 修改混拼 -->
                      <el-form-item :label="'混拼'" :prop="`printList.${index}.freelayout`">
                        <el-switch v-model="item.freelayout" inline-prompt active-value="true" inactive-value="false"
                          active-text="是" inactive-text="否" />
                      </el-form-item>

                      <el-form-item label="拼版方案" :prop="`printList.${index}.imposeType`"
                        :rules="{ required: true, message: '请选择拼版方案', trigger: 'change' }">
                        <!-- <template #label>
                            <span>
                              <el-tooltip content="F4-1/F4-3文件需是4的倍数。F4-2文件页数必须是偶数" placement="top">
                                  <el-icon><question-filled /></el-icon>
                              </el-tooltip>拼版方案
                            </span>
                        </template> -->
                        <el-select v-model="item.imposeType" filterable placeholder="请选择拼版方案" size="small"
                          @change="changeImposeType($event, index)" style="width: 100%">
                          <el-option v-for="pattern in getFilteredLayoutPatterns(item)" :key="pattern.uuid"
                            :label="formatLayoutPattern(pattern)" :value="pattern.uuid.toString()">
                            <div class="layout-pattern-option">
                              <div class="pattern-main">
                                <span :style="{ color: pattern.machineType === 0 ? '#67C23A' : '#E6A23C' }">
                                  {{ pattern.machineType === 0 ? '单张' : '轮转' }}
                                </span>
                                <span>{{ pattern.layoutPattern }}</span>
                                <span>{{ pattern.pageType }}</span>
                                <span>{{ pattern.imposeType }}</span>
                              </div>
                              <div class="pattern-sub" v-if="pattern.productMemo">
                                <span class="product-type">适用：{{ pattern.productMemo }}</span>
                              </div>
                            </div>
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <!-- 每贴页数选择器，仅DP-F4-3方案显示 -->
                      <el-form-item v-if="item.showPagesPerSig" :label="'每贴页数'" :prop="`printList.${index}.pagesPerSig`"
                        :rules="[{ required: true, message: '请选择每贴页数', trigger: 'change' }]">
                        <div class="pagesPerSig-container">
                          <el-select v-model="item.pagesPerSig" placeholder="请选择每贴页数" clearable
                            @change="() => calculateSignatureCount(index)" @clear="item.pagesPerSig = null">
                            <el-option v-for="num in item.pagesPerSigList" :key="num" :label="num" :value="num"
                              :disabled="!item.pages || item.pages <= parseInt(num)">
                              <span style="float: left">{{ num }}</span>
                              <span v-if="item.pages && item.pages > parseInt(num)" class="option-signature-count">
                                {{ getSignatureDisplay(item.pages, parseInt(num)) }}
                              </span>
                              <span v-else-if="item.pages" class="option-warning">
                                (P数需大于{{ num }})
                              </span>
                            </el-option>
                          </el-select>
                          <div v-if="item.pagesPerSig && item.pages && item.pages > item.pagesPerSig"
                            class="selected-signature-count">
                            贴数: {{ getSignatureDisplay(item.pages, item.pagesPerSig) }}
                          </div>
                        </div>
                      </el-form-item>
                    </template>

                    <el-form-item :label="'印刷机型号'" :prop="`printList.${index}.printerTypeId`"
                      :rules="{ required: true, message: '请选择印刷机型号', trigger: 'change' }">
                      <el-select v-model="item.printerTypeId" placeholder="请选择印刷型号" filterable clearable
                        style="width: 100%" @change="(val) => handlePrinterTypeChange(val, index)">
                        <el-option v-for="type in printerTypeList" :key="type.printerTypeId" :label="type.printerType"
                          :value="type.printerTypeId">
                          <span style="float: left">{{ type.printerType }}</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">
                            {{ type.printerVendor }}
                            <el-tooltip content="左右边距" placement="top">
                              <span class="margin-info">
                                <el-icon>
                                  <ArrowLeft />
                                </el-icon>
                                {{ type.marginLf }}
                                <el-icon>
                                  <ArrowRight />
                                </el-icon>
                              </span>
                            </el-tooltip>
                            <el-tooltip content="上下边距" placement="top">
                              <span class="margin-info">
                                <el-icon>
                                  <ArrowUp />
                                </el-icon>
                                {{ type.marginTb }}
                                <el-icon>
                                  <ArrowDown />
                                </el-icon>
                              </span>
                            </el-tooltip>
                          </span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </el-col>

                <!-- 印刷工艺部分 -->
                <el-col :span="16">
                  <div class="craft-section">
                    <div class="info-section" v-if="item.printCraftList != null && item.printCraftList.length > 0">
                      <el-collapse v-model="data.activeCraftNames">
                        <el-collapse-item name="printCrafts">
                          <template #title>
                            <div class="collapse-title-wrapper">
                              <span class="collapse-title">印刷工艺</span>
                              <span v-if="getSelectedCrafts(item.printCraftList)" class="selected-crafts">
                                ({{ getSelectedCrafts(item.printCraftList) }})
                              </span>
                            </div>
                          </template>
                          <el-table :data="item.printCraftList" style="width: 100%" size="small" :header-cell-style="{
                            background: '#f8f9fa',
                            color: '#606266',
                            fontWeight: 500
                          }" border>
                            <el-table-column label="工艺名称" width="190">
                              <template #default="scope">
                                <el-checkbox v-model="scope.row.printCraftChecked" :true-value="'Y'" :false-value="'N'"
                                  @change="(val) => handlePrintCraftChange(scope.row.craftUuid, scope.row)">
                                  {{ scope.row.craftName }}
                                </el-checkbox>
                              </template>
                            </el-table-column>
                            <el-table-column label="单价" width="300">
                              <template #default="scope">
                                <div v-if="scope.row.printCraftChecked === 'Y'" class="price-input">
                                  <div class="price-row">
                                    <template
                                      v-if="scope.row.originalUnitPrice && scope.row.originalUnitPrice !== scope.row.unitPrice">
                                      <span class="original-price">{{ scope.row.originalUnitPrice?.toFixed(2) }}</span>
                                      <el-tag size="small" type="success" effect="plain" class="discount-tag">
                                        {{ ((scope.row.unitPrice / scope.row.originalUnitPrice) * 100).toFixed(0) }}%
                                      </el-tag>
                                    </template>
                                    <template v-else>
                                      <el-tag size="small" type="info" effect="plain" class="discount-tag">
                                        100%
                                      </el-tag>
                                    </template>
                                    <el-input-number v-model="scope.row.unitPrice" :model-value="scope.row.unitPrice"
                                      :min="0" :max="9999" :precision="2" :step="0.1" controls-position="right"
                                      size="small" style="width: 130px"
                                      @update:model-value="(val) => handlePriceUpdate(val, scope.row)" />
                                    <span class="unit-text">元/{{ scope.row.billUnit }}</span>
                                  </div>
                                </div>
                                <span v-else class="disabled-text">-</span>
                              </template>
                            </el-table-column>
                            <el-table-column label="保底价（元）" width="110" align="right">
                              <template #default="scope">
                                <span v-if="scope.row.printCraftChecked === 'Y'" class="price-text">
                                  {{ scope.row.minPrice }}
                                </span>
                                <span v-else class="disabled-text">-</span>
                              </template>
                            </el-table-column>

                            <el-table-column label="计算公式" show-overflow-tooltip>
                              <template #default="scope">
                                <span v-if="scope.row.printCraftChecked === 'Y'">
                                  {{ scope.row.formulaScriptDesc }}
                                </span>
                                <span v-else class="disabled-text">-</span>
                              </template>
                            </el-table-column>
                          </el-table>
                        </el-collapse-item>
                      </el-collapse>
                    </div>
                  </div>
                </el-col>

              </el-row>


            </div>
          </div>
        </template>

        <!-- 装订和后期制作部分 -->
        <div class="craft-section" v-if="form.craftList && form.craftList.length > 0">
          <div v-for="(item, index) in form.craftList" :key="index" class="craft-group">
            <div class="craft-group-header">
              <span class="group-title">{{ item.partName }}</span>
              <el-button type="danger" icon="Delete" circle size="small" @click="handleDeleteCraft(index)" />
            </div>

            <el-row :gutter="20">
              <!-- 装订工艺 -->
              <el-col :span="12" v-if="item.bindingList?.length > 0">
                <el-collapse v-model="data.activeBindingNames">
                  <el-collapse-item name="binding">
                    <template #title>
                      <div class="collapse-title-wrapper">
                        <span class="collapse-title">装订工艺</span>
                        <span v-if="getSelectedBinding(item.bindingList)" class="selected-crafts">
                          ({{ getSelectedBinding(item.bindingList) }})
                        </span>
                      </div>
                    </template>
                    <el-card class="craft-card" shadow="never">
                      <div class="craft-list">
                        <div v-for="craft in item.bindingList" :key="craft.craftUuid" class="craft-item">
                          <div class="craft-content">
                            <el-radio v-model="item.selectedCraftUuid" :value="craft.craftUuid"
                              @change="() => handleBindingChange(craft)">
                              {{ craft.craftName }}
                            </el-radio>

                            <div class="craft-input" v-if="item.selectedCraftUuid === craft.craftUuid">
                              <div class="price-row">
                                <template v-if="craft.originalUnitPrice && craft.originalUnitPrice !== craft.unitPrice">
                                  <span class="original-price">{{ craft.originalUnitPrice?.toFixed(2) }}</span>
                                  <el-tag size="small" type="success" effect="plain" class="discount-tag">
                                    {{ ((craft.unitPrice / craft.originalUnitPrice) * 100).toFixed(0) }}%
                                  </el-tag>
                                </template>
                                <template v-else>
                                  <el-tag size="small" type="info" effect="plain" class="discount-tag">
                                    100%
                                  </el-tag>
                                </template>
                                <el-input-number v-model="craft.unitPrice" :min="0" :max="99999" :precision="2"
                                  :step="0.1" controls-position="right" size="small" style="width: 130px"
                                  @change="validateNumberInput(craft.unitPrice, craft)" />
                                <span class="unit-text">元/{{ craft.billUnit }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </el-collapse-item>
                </el-collapse>
              </el-col>

              <!-- 后期制作 -->
              <el-col :span="12" v-if="item.laterMarkList?.length > 0">
                <el-collapse v-model="data.activeLaterNames">
                  <el-collapse-item name="laterMark">
                    <template #title>
                      <div class="collapse-title-wrapper">
                        <span class="collapse-title">后期制作</span>
                        <span v-if="getSelectedLaterMark(item.laterMarkList)" class="selected-crafts">
                          ({{ getSelectedLaterMark(item.laterMarkList) }})
                        </span>
                      </div>
                    </template>
                    <el-card class="craft-card" shadow="never">
                      <div class="craft-list">
                        <div v-for="craft in item.laterMarkList" :key="craft.craftUuid" class="craft-item">
                          <div class="craft-content">
                            <el-checkbox v-model="craft.printCraftChecked" :true-value="'Y'" :false-value="'N'"
                              @change="() => handleLaterMarkChange(craft)">
                              {{ craft.craftName }}
                            </el-checkbox>

                            <div class="craft-input" v-if="craft.printCraftChecked === 'Y'">
                              <div class="price-row">
                                <template v-if="craft.originalUnitPrice && craft.originalUnitPrice !== craft.unitPrice">
                                  <span class="original-price">{{ craft.originalUnitPrice?.toFixed(2) }}</span>
                                  <el-tag size="small" type="success" effect="plain" class="discount-tag">
                                    {{ ((craft.unitPrice / craft.originalUnitPrice) * 100).toFixed(0) }}%
                                  </el-tag>
                                </template>
                                <template v-else>
                                  <el-tag size="small" type="info" effect="plain" class="discount-tag">
                                    100%
                                  </el-tag>
                                </template>
                                <el-input-number v-model="craft.unitPrice" :min="0" :max="9999" :precision="2"
                                  :step="0.1" controls-position="right" size="small" style="width: 130px"
                                  @change="validateNumberInput(craft.unitPrice, craft)" />
                                <span class="unit-text">元/{{ craft.billUnit }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-card>
                  </el-collapse-item>
                </el-collapse>
              </el-col>
            </el-row>
          </div>
        </div>

        <el-form-item class="form-buttons">
          <el-button type="primary" @click="submitForm" :loading="submitting">下一步</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 产品选择对话框 -->
    <ProductSelectDialog v-model="showProductDialog" @confirm="handleProductSelect" />
  </div>
</template>

<script setup name="OrderAdd">
import { getCurrentInstance, reactive, ref, toRefs, onMounted, onActivated, watch, nextTick, onDeactivated } from 'vue';
import { useRouter } from 'vue-router';
import { useOrderStore } from '@/stores/order';
import useUserStore from '@/store/modules/user';
import { addOrder, getLayouts, getBusinessUploadType } from "@/api/order/order";
import { queryProductPartList, queryLayoutPatternList } from "@/api/product/PrintProductMain";
import { listCustomer, getOptimalDiscountCrafts } from "@/api/business/customer";
import { listAddress } from "@/api/business/address";
import { listPrintFinishedSize } from "@/api/product/PrintFinishedSize";
import { listPrinterType } from "@/api/device/PrinterType";
import ProductSelectDialog from './components/ProductSelectDialog.vue';
import { listPaperClass } from "@/api/business/paperClass";
import { listPaperMain } from "@/api/business/PaperMain";
import { getPaperPrices } from "@/api/business/PaperPrice";
import PdfUpload from './components/PdfUpload.vue';
import { getQiniuToken } from "@/api/system/qiniu";
import { ElMessage } from 'element-plus';
import { ArrowLeft, ArrowRight, ArrowUp, ArrowDown, InfoFilled, FolderOpened, Upload, Document, Check, Refresh, Link } from '@element-plus/icons-vue';
import { getAllUserList } from "@/api/system/user";
import useTagsViewStore from '@/store/modules/tagsView';
import SharedFolderDialog from './components/SharedFolderDialog.vue'

const { proxy } = getCurrentInstance();
const { m204, m205, m207, m209, m212 } = proxy.useDict('m204', 'm205', 'm207', 'm209', 'm212');

const router = useRouter();
const orderStore = useOrderStore();
const userStore = useUserStore();
console.log('userStore', userStore);

const showProductDialog = ref(false);
const selectedProduct = ref(null);
const customerList = ref([]);
const addressList = ref([]);
const layoutPatternList = ref([]);
const paperClassList = ref([]);
const finishedSizeList = ref([]);
const defaultTime = new Date(2000, 1, 1, 12, 0, 0)

// 添加 loading 状态
const submitting = ref(false);
const uploadType = ref('')
const uploadPath = ref('')
// 七牛云配置
const qiniuToken = ref('');
const qiniuConfig = ref({
  region: 'z0',
  useCdnDomain: true
});
const qiniuHost = ref('');
const qiniuBucket = ref('');
const qiniuFilePathPrefix = ref('');

const printerTypeList = ref([]); // 添加印刷型号列表
const userList = ref([]); // 添加用户列表

const data = reactive({
  form: {
    orderNo: null,
    customerUuid: null,
    customerName: null,
    contact: null,
    phone: null,
    addressUuid: null,
    address: null,
    isPickup: 'N', // 添加自提字段，默认为N
    payType: null,
    priority: 0,
    finishedSizeType: 1, // 1: 选择文件尺寸, 2: 自定义尺寸
    finishedSizeUuid: null,
    paperWidth: null,
    paperHeight: null,
    finishedSize: null,
    customerRequire: '', // 客户要求字段
    printList: [],
    craftList: [],
    orderNum: 1,
    salesUserId: null, // 销售人员，在用户列表加载后设置
  },
  // 添加折叠面板的激活项数组
  activeCraftNames: [],
  activeBindingNames: [],
  activeLaterNames: [],
  rules: {
    customerUuid: [{ required: true, message: "客户不能为空", trigger: "change" }],
    salesUserId: [{ required: true, message: "销售人员不能为空", trigger: "change" }],
    contact: [{ required: true, message: "联系人不能为空", trigger: "blur" }],
    phone: [{ required: true, message: "联系电话不能为空", trigger: "blur" }],
    addressUuid: [{ required: true, message: "配送地址不能为空", trigger: "blur" }],
    // payType: [{ required: true, message: "支付方式不能为空", trigger: "change" }],
    priority: [{ required: true, message: "优先级不能为空", trigger: "change" }],
    orderNum: [{ required: true, message: "订单份数不能为空", trigger: "change" }],
  },
});

const { form, rules } = toRefs(data);

const selectedCustomer = ref(null);
const discountList = ref([]);

/** 应用折扣到各个工艺价格 */
const applyDiscounts = (discounts) => {
  if (!discounts?.length || !form.value.printList) {
    return;
  }

  // 遍历所有折扣并应用
  discounts.forEach(discount => {
    const discountRate = discount.discountRate || 1;

    switch (discount.craftClassType) {
      // 印刷工艺折扣
      case '2':
        form.value.printList.forEach(print => {
          print.printCraftList?.forEach(craft => {
            if (!craft.originalUnitPrice) {
              craft.originalUnitPrice = craft.unitPrice;
            }
            craft.unitPrice = parseFloat((craft.originalUnitPrice * discountRate).toFixed(2));
          });
        });
        break;

      // 装订工艺折扣
      case '1':
        form.value.craftList?.forEach(craftGroup => {
          // 装订工艺
          craftGroup.bindingList?.forEach(craft => {
            if (!craft.originalUnitPrice) {
              craft.originalUnitPrice = craft.unitPrice;
            }
            craft.unitPrice = parseFloat((craft.originalUnitPrice * discountRate).toFixed(2));
          });

          // 后期制作工艺
          craftGroup.laterMarkList?.forEach(craft => {
            if (!craft.originalUnitPrice) {
              craft.originalUnitPrice = craft.unitPrice;
            }
            craft.unitPrice = parseFloat((craft.originalUnitPrice * discountRate).toFixed(2));
          });
        });
        break;
      // 纸张单价折扣
      case '9999':
        form.value.printList.forEach(print => {
          if (print.originalPrintUnitPrice) {
            print.printUnitPrice = parseFloat((print.originalPrintUnitPrice * discountRate).toFixed(2));
          }
        });
        break;
    }
  });
};

/** 获取最优折扣工艺列表 */
const fetchOptimalDiscountCrafts = async () => {
  if (!form.value.customerUuid || !form.value.orderNum) {
    discountList.value = [];
    return;
  }

  try {
    const query = {
      customerUuid: form.value.customerUuid,
      orderNum: parseInt(form.value.orderNum)
    };

    const response = await getOptimalDiscountCrafts(query);
    if (response.code === 200) {
      discountList.value = response.data || [];
      applyDiscounts(discountList.value);
    }
  } catch (error) {
    console.error('获取折扣工艺列表失败:', error);
    proxy.$modal.msgError('获取折扣工艺列表失败');
    discountList.value = [];
  }
};

// 监听客户选择和订单数量变化
watch([() => form.value.customerUuid, () => form.value.orderNum], () => {
  if (form.value.customerUuid && form.value.orderNum) {
    fetchOptimalDiscountCrafts();
  } else {
    discountList.value = [];
  }
}, { immediate: true });

/** 处理产品选择 */
async function handleProductSelect(product) {
  selectedProduct.value = product;
  // 加载产品明细
  try {
    const { data } = await queryProductPartList(product.uuid);
    if (data) {
      // 处理打印列表数据
      const printList = data.printList || [];
      printList.forEach(item => {
        // 保持原有数据结构
        item.orderNum = 1;
        item.productName = item.productName || '';
        item.partName = item.partName || '';

        // 设置默认值，使用数字类型
        item.isColorPrint = item.isColorPrint ?? 1; // 默认彩色
        item.isSinglePrint = item.isSinglePrint ?? 1; // 默认单面
        item.isMakeup = item.isMakeup || 'Y';
        item.freelayout = item.freelayout || 'false'; // 设置混拼默认值为字符串'false'
        item.printUnitPrice = 0; // 初始化价格为0

        // 初始化纸张相关字段
        item.classUuid = item.classUuid || null;
        item.className = item.className || null;
        item.paperUuid = item.paperUuid || null;
        item.paperName = item.paperName || null;
        item.makeupSize = item.makeupSize || null;
        item.paperList = item.paperList || [];
        // 添加文件尺寸字段
        item.paperWidth = form.value.paperWidth;
        item.paperHeight = form.value.paperHeight;
        // 添加P字段
        item.pages = item.pages || null;
        // 添加印刷机型号字段
        item.printerTypeId = item.printerTypeId || null;
        item.printerType = item.printerType || null;

        // 只有在选择拼版时设置这些值
        if (item.isMakeup === 'Y') {
          item.isCut = item.isCut || 'Y';
          item.bleedMargin = item.bleedMargin ? Number(item.bleedMargin) : 3;
          item.bindingMargin = typeof item.bindingMargin === 'number' ? item.bindingMargin : parseInt(item.bindingMargin) || 2;

          // 如果已有imposeType，使用现有的，否则设置默认值
          if (item.imposeType) {
            // 确保imposeType是字符串类型
            item.imposeType = item.imposeType.toString();
            // 获取对应的拼版方案
            const selectedPattern = getSelectedPattern(item.imposeType);
            if (selectedPattern) {
              item.machineType = selectedPattern.machineType;
              item.layoutPattern = selectedPattern.layoutPattern;
              // 检查是否是DP-F4-3方案
              if (selectedPattern.layoutPattern === 'DP-F4-3') {
                item.showPagesPerSig = true;
                item.pagesPerSigList = ['8', '12', '16', '20', '24', '32'];
                // 不设置默认值，保持为 null，只在显示时才有默认值
                item.pagesPerSig = null;
              }
            }
          } else if (layoutPatternList.value.length > 0) {
            // 如果没有imposeType，设置默认值
            const defaultPattern = layoutPatternList.value[0];
            item.imposeType = defaultPattern.uuid.toString();
            item.machineType = defaultPattern.machineType;
            item.layoutPattern = defaultPattern.layoutPattern;
          }
        }

        // 确保拼版相关列表存在
        item.layoutPatternList = item.layoutPatternList || [];
        item.imposeTypeList = item.imposeTypeList || [];

        // 初始化印刷工艺列表
        if (item.printCraftList) {
          item.printCraftList.forEach(craft => {
            // 保存原始单价
            craft.originalUnitPrice = craft.unitPrice;
            // 如果是必选项或已选中，应用折扣
            if (craft.isMust === 'Y' || craft.printCraftChecked === 'Y') {
              craft.printCraftChecked = 'Y';
              // 获取印刷工艺折扣（craftClassType: '2'）
              const printCraftDiscount = discountList.value?.find(d => d.craftClassType === '2');
              if (printCraftDiscount && printCraftDiscount.discountRate) {
                // 应用折扣
                craft.unitPrice = parseFloat((craft.originalUnitPrice * printCraftDiscount.discountRate).toFixed(2));
              }
            }
          });
        }
      });

      form.value.printList = printList;
      form.value.craftList = data.craftList || [];

      // 处理装订工艺和后期制作的默认值
      if (data.craftList) {
        data.craftList.forEach(item => {
          // 处理装订工艺
          if (item.bindingList) {
            // 找到printCraftChecked为Y的项
            const defaultSelected = item.bindingList.find(craft => craft.printCraftChecked === 'Y');
            if (defaultSelected) {
              // 设置选中的UUID
              item.selectedCraftUuid = defaultSelected.craftUuid;
              console.log('设置默认选中的装订工艺:', {
                craftName: defaultSelected.craftName,
                craftUuid: defaultSelected.craftUuid,
                printCraftChecked: defaultSelected.printCraftChecked
              });
            }
          }

          // 处理后期制作工艺的默认选中状态
          if (item.laterMarkList) {
            item.laterMarkList.forEach(craft => {
              // 保存原始单价
              craft.originalUnitPrice = craft.unitPrice;
              // 设置默认选中状态
              if (craft.printCraftChecked === 'Y') {
                console.log('设置默认选中的后期制作:', {
                  craftName: craft.craftName,
                  craftUuid: craft.craftUuid,
                  printCraftChecked: craft.printCraftChecked
                });
              }
            });
          }
        });
      }
    }
  } catch (error) {
    proxy.$modal.msgError("加载产品明细失败");
  }

  // 关闭产品选择对话框
  showProductDialog.value = false;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["orderRef"].validate(valid => {
    if (valid) {
      if (!selectedProduct.value) {
        proxy.$modal.msgError("请选择产品");
        return;
      }

      // 检查不拼版的打印项是否上传了文件
      const noMakeupItems = form.value.printList.filter(item => item.isMakeup === 'N');
      if (noMakeupItems.length > 0) {
        const missingFiles = noMakeupItems.some(item => !item.fileUrl || !Array.isArray(item.fileUrl) || item.fileUrl.length === 0);
        if (missingFiles) {
          proxy.$modal.msgError("不拼版时必须上传文件");
          return;
        }
      }

      // 如果正在提交中，直接返回
      if (submitting.value) {
        return;
      }

      submitting.value = true;

      // 构建提交数据
      const submitData = {
        ...form.value,
        productMainUuid: selectedProduct.value.uuid,
        customerRequire: form.value.customerRequire,
        uploadType: uploadType,
        craftList: form.value.craftList?.map(item => {
          // 确保选中的装订工艺的printCraftChecked为Y
          if (item.bindingList) {
            item.bindingList.forEach(craft => {
              if (craft.craftUuid === item.selectedCraftUuid) {
                craft.printCraftChecked = 'Y';
              } else {
                craft.printCraftChecked = 'N';
              }
            });
          }

          return {
            ...item,
            // 只保留选中的装订工艺，并确保printCraftChecked为Y
            bindingList: item.bindingList?.filter(craft => {
              const isSelected = craft.craftUuid === item.selectedCraftUuid;
              if (isSelected) {
                craft.printCraftChecked = 'Y';
                console.log('选中的装订工艺:', {
                  craftName: craft.craftName,
                  craftUuid: craft.craftUuid,
                  unitPrice: craft.unitPrice,
                  printCraftChecked: craft.printCraftChecked
                });
              }
              return isSelected;
            }),
            // 只保留选中的后期制作工艺
            laterMarkList: item.laterMarkList?.filter(craft => {
              const isSelected = craft.printCraftChecked === 'Y';
              if (isSelected) {
                console.log('选中的后期制作:', {
                  craftName: craft.craftName,
                  craftUuid: craft.craftUuid,
                  unitPrice: craft.unitPrice,
                  printCraftChecked: craft.printCraftChecked
                });
              }
              return isSelected;
            })
          };
        }) || [],
        printList: form.value.printList.map(item => {
          // 基础数据
          const printItem = {
            ...item,
            paperName: item.paperName || null,
            pages: parseInt(item.pages) || 0,
            orderNum: parseInt(form.value.orderNum) || 1,
            printUnitPrice: item.printUnitPrice !== undefined ? parseFloat(item.printUnitPrice) : 100 // 修改这里确保价格被正确传递
          };

          // 根据是否拼版处理相关字段
          if (item.isMakeup === 'Y') {
            // 保持 pagesPerSig 的原始值，如果为 null 则保持 null，否则转换为整数
            printItem.pagesPerSig = item.pagesPerSig !== null && item.pagesPerSig !== undefined ? parseInt(item.pagesPerSig) : null;
            printItem.isCut = item.isCut;
            printItem.bleedMargin = parseFloat(item.bleedMargin);
            printItem.bindingMargin = parseInt(item.bindingMargin);
            printItem.freelayout = item.freelayout;
            printItem.imposeType = item.imposeType;
            printItem.machineType = item.machineType;
          } else {
            // 不拼版时，相关字段设为null或默认值
            printItem.pagesPerSig = null;
            printItem.isCut = null;
            printItem.bleedMargin = null;
            printItem.bindingMargin = null;
            printItem.freelayout = 'false';
            printItem.imposeType = null;
            printItem.machineType = null;
          }

          // 移除不需要传递的字段
          delete printItem.paperPrices;
          delete printItem.paperList;
          delete printItem.sizeWarning;
          delete printItem.showPagesPerSig;
          delete printItem.pagesPerSigList;
          delete printItem.makeupPageLayout;
          delete printItem.jobData;
          delete printItem.selectedLayouts;
          delete printItem.priceDescription;

          console.log('提交的打印项数据:', {
            pageUnitPrice: printItem.printUnitPrice,
            paperName: printItem.paperName,
            pages: printItem.pages
          });

          return printItem;
        })
      };



      // 先保存基础数据到store
      orderStore.saveOrderData({
        orderForm: submitData,
        selectedProduct: selectedProduct.value,
        layouts: null // 先设置为null，等获取到拼版方案后再更新
      });

      // 获取拼版方案
      getLayouts(submitData).then(response => {
        if (!response.data || !response.data.printList) {
          proxy.$modal.msgError("获取拼版方案失败");
          submitting.value = false;
          return;
        }

        // 确保数据类型正确
        const layoutsData = {
          ...response.data,
          printList: response.data.printList.map(item => ({
            ...item,
            // 根据是否拼版处理字段，保持 pagesPerSig 的原始值
            pagesPerSig: item.isMakeup === 'Y' ?
              (item.pagesPerSig !== null && item.pagesPerSig !== undefined ? parseInt(item.pagesPerSig) : null) :
              null,
            pages: parseInt(item.pages) || 0,
            surface: parseInt(item.surface) || 0,
            orderNum: parseInt(form.value.orderNum) || 1
          }))
        };

        // 保存拼版方案到store
        orderStore.saveOrderData({
          orderForm: submitData,
          selectedProduct: selectedProduct.value,
          layouts: layoutsData
        });

        console.log('保存到store的数据:', {
          orderForm: submitData,
          selectedProduct: selectedProduct.value,
          layouts: layoutsData
        });

        // 跳转到确认页面
        router.push('/order/order/confirm');
      }).catch(error => {
        console.error('获取拼版方案失败:', error);
        proxy.$modal.msgError("获取拼版方案失败");
        submitting.value = false;
      });
    }
  });
}

/** 取消按钮 */
function cancel() {
  // 1. 清除 store 中的数据
  orderStore.clearOrderData();

  // 2. 清除 localStorage 和 sessionStorage 中的数据
  const storageKeys = [
    'order-data',  // pinia 持久化的 key
    'orderData',
    'selectedProduct',
    'layouts',
    'orderForm',
    'printList',
    'craftList'
  ];

  storageKeys.forEach(key => {
    localStorage.removeItem(key);
    sessionStorage.removeItem(key);
  });

  // 3. 清除 tagsView 中的缓存
  const tagsViewStore = useTagsViewStore();
  const pageName = 'OrderAdd';
  const index = tagsViewStore.cachedViews.indexOf(pageName);
  if (index > -1) {
    tagsViewStore.cachedViews.splice(index, 1);
  }

  // 4. 重置表单数据
  form.value = {
    orderNo: null,
    customerUuid: null,
    customerName: null,
    contact: null,
    phone: null,
    addressUuid: null,
    address: null,
    isPickup: 'N',
    payType: null,
    priority: 0,
    finishedSizeType: 1,
    finishedSizeUuid: null,
    paperWidth: null,
    paperHeight: null,
    finishedSize: null,
    customerRequire: '',
    printList: [],
    craftList: [],
    orderNum: 1,
    salesUserId: null,
  };

  // 5. 重置其他相关数据
  selectedProduct.value = null;

  // 6. 关闭当前页面并返回订单列表
  proxy.$tab.closeOpenPage({ path: '/order/order' });
}

/** 获取拼版方案 */
async function getLayoutPatternList() {
  try {
    const res = await queryLayoutPatternList({ pageSize: 9999 });
    layoutPatternList.value = res.rows || [];
  } catch (error) {
    proxy.$modal.msgError("获取拼版方案失败");
  }
}

/** 根据纸张类型过滤拼版方案 */
const getFilteredLayoutPatterns = (item) => {

  if (!layoutPatternList.value || !item.paperCategory) {
    return layoutPatternList.value || [];
  }

  const paperCategory = item.paperCategory.toLowerCase();
  // console.log('纸张类型:', paperCategory);

  const filteredPatterns = layoutPatternList.value.filter(pattern => {
    if (paperCategory === 'roll') {
      return pattern.machineType === 1; // 轮转机
    } else if (paperCategory === 'sheet') {
      return pattern.machineType === 0; // 数码机（单页）
    }
    return true; // 如果没有匹配的paperCategory，显示所有方案
  });

  // console.log('过滤后的拼版方案:', filteredPatterns);
  return filteredPatterns;
};

/** 获取客户列表 */
async function getCustomerList() {
  try {
    const res = await listCustomer({ pageSize: 9999 });
    customerList.value = res.rows || [];
  } catch (error) {
    proxy.$modal.msgError("获取客户列表失败");
  }
}

/** 根据客户ID获取地址列表 */
async function getAddressList(customerUuid) {
  if (!customerUuid) {
    addressList.value = [];
    return;
  }
  try {
    const res = await listAddress({
      pageSize: 9999,
      customerUuid: customerUuid
    });
    addressList.value = res.rows || [];
  } catch (error) {
    proxy.$modal.msgError("获取地址列表失败");
  }
}

/** 处理客户选择变更 */
const handleCustomerChange = async (customerUuid) => {
  // 清空相关字段
  form.value.contact = '';
  form.value.phone = '';
  form.value.addressUuid = '';
  form.value.address = '';
  form.value.isPickup = 'N'; // 重置自提状态

  if (!customerUuid) {
    selectedCustomer.value = null;
    return;
  }

  // 从客户列表中查找选中的客户信息
  selectedCustomer.value = customerList.value.find(item => item.uuid === customerUuid);
  if (selectedCustomer.value) {
    form.value.customerName = selectedCustomer.value.customerName;
    form.value.contact = selectedCustomer.value.contact;
    form.value.phone = selectedCustomer.value.phone;
  }

  // 获取该客户的地址列表
  await getAddressList(customerUuid);

  // 如果有地址列表，自动选择默认地址或第一个地址
  if (addressList.value && addressList.value.length > 0) {
    // 查找默认地址（isDefault === 'Y'）
    const defaultAddress = addressList.value.find(addr => addr.isDefault === 'Y');

    if (defaultAddress) {
      // 如果有默认地址，选择默认地址
      form.value.addressUuid = defaultAddress.uuid;
      form.value.address = defaultAddress.address;
      // 如果地址中包含联系人和电话，且当前表单中没有这些信息，则自动填充
      if (!form.value.contact && defaultAddress.contact) {
        form.value.contact = defaultAddress.contact;
      }
      if (!form.value.phone && defaultAddress.phone) {
        form.value.phone = defaultAddress.phone;
      }
    } else {
      // 如果没有默认地址，选择第一个地址
      const firstAddress = addressList.value[0];
      form.value.addressUuid = firstAddress.uuid;
      form.value.address = firstAddress.address;
      // 如果地址中包含联系人和电话，且当前表单中没有这些信息，则自动填充
      if (!form.value.contact && firstAddress.contact) {
        form.value.contact = firstAddress.contact;
      }
      if (!form.value.phone && firstAddress.phone) {
        form.value.phone = firstAddress.phone;
      }
    }
  }
};

/** 处理地址选择变化 */
function handleAddressChange(addressUuid) {
  if (!addressUuid) {
    form.value.address = '';
    return;
  }

  const selectedAddress = addressList.value.find(item => item.uuid === addressUuid);
  if (selectedAddress) {
    form.value.address = selectedAddress.address;
    // 如果地址中包含联系人和电话，且当前表单中没有这些信息，则自动填充
    if (!form.value.contact && selectedAddress.contact) {
      form.value.contact = selectedAddress.contact;
    }
    if (!form.value.phone && selectedAddress.phone) {
      form.value.phone = selectedAddress.phone;
    }
  }
}

/** 删除部件 */
function handleDeletePart(index) {
  proxy.$modal.confirm('是否确认删除该部件？').then(() => {
    form.value.printList.splice(index, 1);
  }).catch(() => { });
}

/** 处理拼版选择变化 */
const handleImpositionChange = (value, index) => {
  const item = form.value.printList[index];
  if (value === 'N') {
    // 清空相关字段
    item.isCut = null;
    item.bleedMargin = null;
    item.bindingMargin = null;
    item.impositionPlan = null;
    item.imposeType = null;
    item.machineType = null;
    item.showPagesPerSig = false;
    item.pagesPerSig = null;
    item.freelayout = 'false';
    // 清空拼版相关数据
    item.makeupPageLayout = null;
    // 清空印刷机型号相关字段
    item.printerTypeId = null;
    item.printerType = null;

    // 添加提示
    ElMessage({
      message: '不拼版时必须上传文件',
      type: 'warning'
    });
  } else {
    // 设置默认值
    item.isCut = 'Y';
    item.bleedMargin = 3;
    item.bindingMargin = 2;
    item.freelayout = 'false';
    // 如果有默认拼版方案，设置它
    if (layoutPatternList.value.length > 0) {
      const defaultPattern = layoutPatternList.value[0];
      item.imposeType = defaultPattern.uuid + '';
      item.machineType = defaultPattern.machineType;
      // 检查是否是 DP-F4-3 方案
      if (defaultPattern.layoutPattern === 'DP-F4-3') {
        item.showPagesPerSig = true;
        item.pagesPerSigList = ['8', '12', '16', '20', '24', '32'];
        // 清空每贴页数，不设置默认值
        item.pagesPerSig = null;
      } else {
        item.showPagesPerSig = false;
        item.pagesPerSig = null;
      }
    }
  }
  console.log('拼版设置变更:', item);
};

/** 格式化拼版方案显示 */
const formatLayoutPattern = (pattern) => {
  const machineType = pattern.machineType === 0 ? '单张 ' : '轮转 ';
  return `${machineType}-${pattern.layoutPattern} - ${pattern.pageType} - ${pattern.imposeType}`;
};

/** 获取选中的拼版方案完整信息 */
const getSelectedLayoutPattern = (uuid) => {
  const pattern = getSelectedPattern(uuid);
  if (!pattern) return '';

  const machineType = pattern.machineType === 0 ? '单张 ' : '轮转 ';
  return `${machineType} | ${pattern.pageType} | ${pattern.imposeType}`;
};

/** 获取选中的拼版方案对象 */
const getSelectedPattern = (uuid) => {
  if (!uuid) return null;
  // 将uuid转换为数字进行比较
  const numericUuid = typeof uuid === 'string' ? parseInt(uuid) : uuid;
  return layoutPatternList.value.find(p => p.uuid === numericUuid);
};

/** 处理拼版方案变更 */
const changeImposeType = (uuid, index) => {
  const item = form.value.printList[index];
  item.imposeType = uuid.toString(); // 确保存储为字符串类型
  // 获取选中的拼版方案
  const selectedPattern = getSelectedPattern(uuid);
  if (selectedPattern) {
    item.machineType = selectedPattern.machineType;
    item.layoutPattern = selectedPattern.layoutPattern; // 添加这行，保存layoutPattern
    // DP-F4-3方案时显示每贴页数
    if (selectedPattern.layoutPattern === 'DP-F4-3') {
      item.showPagesPerSig = true;
      item.pagesPerSigList = ['8', '12', '16', '20', '24', '32'];
      // 清空每贴页数，不设置默认值
      item.pagesPerSig = null;
      // 检查页数条件，如果不满足也清空每贴页数
      if (!item.pages || (item.pagesPerSigList && item.pagesPerSigList.length > 0 &&
        item.pages <= Math.min(...item.pagesPerSigList.map(n => parseInt(n))))) {
        item.pagesPerSig = null;
      }
    } else {
      item.showPagesPerSig = false;
      item.pagesPerSig = null;
    }
    console.log('选中的拼版方案:', selectedPattern, '每贴页数:', item.pagesPerSig);
  }
};

/** 获取纸张种类列表 */
async function getPaperClassList() {
  try {
    const res = await listPaperClass({ pageSize: 9999 });
    paperClassList.value = res.rows || [];
  } catch (error) {
    proxy.$modal.msgError("获取纸张种类列表失败");
  }
}

/** 根据种类获取纸张列表 */
async function getPaperList(classUuid, index) {
  if (!classUuid) {
    form.value.printList[index].paperList = [];
    return;
  }
  try {
    const res = await listPaperMain({
      pageSize: 9999,
      paperClassUuid: classUuid
    });
    console.log('获取纸张列表:', res.rows);
    // 确保每个纸张对象都有正确的属性
    form.value.printList[index].paperList = (res.rows || []).map(paper => ({
      ...paper,
      uuid: String(paper.uuid),
      paperCategory: paper.paperCategory || 'sheet' // 确保有默认值
    }));
  } catch (error) {
    proxy.$modal.msgError("获取纸张列表失败");
  }
}

/** 处理纸张种类选择变化 */
async function handlePaperClassChange(classUuid, index) {
  const item = form.value.printList[index];
  // 清空相关字段
  item.paperUuid = null;
  item.paperName = null;
  item.makeupSize = null;  // 改为 makeupSize

  if (!classUuid) return;

  // 查找选中的纸张种类信息
  const selectedClass = paperClassList.value.find(cls => cls.uuid === classUuid);
  if (selectedClass) {
    item.classUuid = selectedClass.uuid;
    item.className = selectedClass.className;
  }

  // 获取该种类的纸张列表
  await getPaperList(classUuid, index);
}

/** 处理纸张选择变化 */
const handlePaperChange = async (paperUuid, index) => {
  const item = form.value.printList[index];
  if (!paperUuid) {
    // 清空相关数据
    item.paperName = '';
    item.makeupSize = '';
    item.sizeWarning = '';
    item.paperPrices = [];
    item.printUnitPrice = 0;
    item.originalPrintUnitPrice = 0;
    item.paperCategory = null;
    // 清空拼版方案
    item.imposeType = null;
    item.machineType = null;
    item.layoutPattern = null;
    return;
  }

  // 获取选中的纸张信息
  const selectedPaper = item.paperList?.find(p => p.uuid === paperUuid);
  console.log('Selected paper:', selectedPaper);

  if (selectedPaper) {
    item.paperName = selectedPaper.paperName;
    item.makeupSize = selectedPaper.propertyValue;
    item.paperCategory = selectedPaper.paperCategory; // 设置纸张类型

    // 检查当前选择的拼版方案是否仍然可用
    if (item.imposeType) {
      const currentPattern = layoutPatternList.value.find(p => p.uuid.toString() === item.imposeType);
      if (currentPattern) {
        const paperCategory = selectedPaper.paperCategory?.toLowerCase();
        const isValidPattern = (paperCategory === 'roll' && currentPattern.machineType === 1) ||
          (paperCategory === 'sheet' && currentPattern.machineType === 0);

        if (!isValidPattern) {
          // 如果当前拼版方案不再适用，清空选择
          item.imposeType = null;
          item.machineType = null;
          item.layoutPattern = null;
        }
      }
    }

    // 检查文件尺寸是否超过纸张尺寸
    nextTick(() => {
      checkPaperSize(item);
    });

    // 获取纸张价格
    try {
      const response = await getPaperPrices(paperUuid);
      if (response.code === 200 && response.data) {
        item.paperPrices = response.data;
        // 根据当前选项找到对应的价格
        const matchedPrice = response.data.find(p =>
          p.isSinglePrint === item.isSinglePrint &&
          p.isColorPrint === item.isColorPrint
        );
        if (matchedPrice) {
          // 保存原始单价
          item.originalPrintUnitPrice = parseFloat(matchedPrice.unitPrice);

          // 获取印刷折扣（craftClassType: '9999'）
          const printDiscount = discountList.value?.find(d => d.craftClassType === '9999');
          if (printDiscount && printDiscount.discountRate) {
            // 应用折扣
            item.printUnitPrice = parseFloat((item.originalPrintUnitPrice * printDiscount.discountRate).toFixed(2));
          } else {
            item.printUnitPrice = item.originalPrintUnitPrice;
          }
        } else {
          item.originalPrintUnitPrice = 0;
          item.printUnitPrice = 0;
        }
      }
    } catch (error) {
      console.error('获取纸张价格失败:', error);
      proxy.$modal.msgError('获取纸张价格失败');
      // 重置价格为0
      item.printUnitPrice = 0;
      item.originalPrintUnitPrice = 0;
    }
  }
};





/** 处理印刷选项变化（色彩或单双面） */
const handlePrintOptionChange = async (index) => {
  const item = form.value.printList[index];
  if (!item || !item.paperPrices || !Array.isArray(item.paperPrices)) {
    console.log('No paper prices available for item:', index);
    return;
  }

  // 根据当前选项找到对应的价格
  const matchedPrice = item.paperPrices.find(p =>
    p.isSinglePrint === item.isSinglePrint &&
    p.isColorPrint === item.isColorPrint
  );

  if (matchedPrice) {
    // 保存原始单价
    item.originalPrintUnitPrice = parseFloat(matchedPrice.unitPrice);

    // 获取印刷折扣（craftClassType: '9999'）
    const printDiscount = discountList.value?.find(d => d.craftClassType === '9999');
    if (printDiscount && printDiscount.discountRate) {
      // 应用折扣
      item.printUnitPrice = parseFloat((item.originalPrintUnitPrice * printDiscount.discountRate).toFixed(2));
    } else {
      item.printUnitPrice = item.originalPrintUnitPrice;
    }

    console.log('Updated print unit price:', {
      originalPrice: item.originalPrintUnitPrice,
      discountRate: printDiscount?.discountRate || 1,
      currentPrice: item.printUnitPrice,
      options: {
        isSinglePrint: item.isSinglePrint,
        isColorPrint: item.isColorPrint
      }
    });
  } else {
    console.warn('No matching price found for current print options');
    item.originalPrintUnitPrice = 0;
    item.printUnitPrice = 0;
  }
};

/** 解析纸张尺寸字符串 */
function parsePaperSize(sizeStr) {
  if (!sizeStr) return null;

  // 1. 首先尝试提取纯数字（移除所有非数字字符）
  const numbers = sizeStr.match(/\d+/g);
  if (numbers && numbers.length >= 2) {
    // 取前两个数字作为宽高
    return {
      width: parseInt(numbers[0]),
      height: parseInt(numbers[1])
    };
  }

  // 2. 如果上面的方法没有找到两个数字，返回null
  return null;
}

/** 检查文件尺寸是否超过纸张尺寸 */
function checkPaperSize(item) {
  // 如果是卷筒纸，直接返回 false（不进行尺寸校验）
  if (item.paperCategory === 'roll') {
    item.sizeWarning = '';
    return false;
  }

  if (!item.makeupSize || !item.paperWidth || !item.paperHeight) {
    console.log('Missing required size information:', { makeupSize: item.makeupSize, paperWidth: item.paperWidth, paperHeight: item.paperHeight });
    item.sizeWarning = '';
    return false;
  }

  const paperSize = parsePaperSize(item.makeupSize);
  if (!paperSize) {
    console.log('Failed to parse paper size:', item.makeupSize);
    item.sizeWarning = '';
    return false;
  }

  console.log('Checking size:', {
    paperWidth: item.paperWidth,
    paperHeight: item.paperHeight,
    makeupSize: item.makeupSize,
    parsedSize: paperSize
  });

  // 检查文件尺寸是否超过纸张尺寸
  const isWidthExceeded = parseFloat(item.paperWidth) > parseFloat(paperSize.width);
  const isHeightExceeded = parseFloat(item.paperHeight) > parseFloat(paperSize.height);

  console.log('Size comparison:', {
    isWidthExceeded,
    isHeightExceeded,
    itemWidth: parseFloat(item.paperWidth),
    itemHeight: parseFloat(item.paperHeight),
    paperWidth: parseFloat(paperSize.width),
    paperHeight: parseFloat(paperSize.height)
  });

  // 设置警告信息
  if (isWidthExceeded || isHeightExceeded) {
    const warning = `文件尺寸(${item.paperWidth}×${item.paperHeight})超过纸张尺寸(${paperSize.width}×${paperSize.height})`;
    console.log('Setting warning:', warning);
    item.sizeWarning = warning;
    // 确保响应式更新
    nextTick(() => {
      console.log('After nextTick - item.sizeWarning:', item.sizeWarning);
    });
  } else {
    item.sizeWarning = '';
  }

  return isWidthExceeded || isHeightExceeded;
}

/** 处理文件尺寸变化 */
function handleItemSizeChange(index) {
  const item = form.value.printList[index];
  console.log('handleItemSizeChange - item:', item);
  console.log('handleItemSizeChange - before check:', {
    index,
    paperWidth: item.paperWidth,
    paperHeight: item.paperHeight,
    makeupSize: item.makeupSize
  });
  // 检查文件尺寸
  nextTick(() => {
    checkPaperSize(item);
  });
}

/** 获取文件尺寸列表 */
async function getFinishedSizeList() {
  try {
    const res = await listPrintFinishedSize({ pageSize: 9999 });
    finishedSizeList.value = res.rows || [];
  } catch (error) {
    proxy.$modal.msgError("获取成品尺寸列表失败");
  }
}

/** 处理文件尺寸类型变化 */
function handleFinishedSizeTypeChange(value) {
  // 清空相关字段
  form.value.finishedSizeUuid = null;
  form.value.paperWidth = null;
  form.value.paperHeight = null;
  form.value.finishedSize = null;
}

/** 处理文件尺寸选择变化 */
function handleFinishedSizeChange(sizeUuid) {
  if (!sizeUuid) {
    form.value.paperWidth = null;
    form.value.paperHeight = null;
    form.value.finishedSize = null;
    // 清空所有打印项的尺寸
    form.value.printList.forEach(item => {
      item.paperWidth = null;
      item.paperHeight = null;
    });
    return;
  }

  const selectedSize = finishedSizeList.value.find(size => size.uuid === sizeUuid);
  if (selectedSize) {
    form.value.paperWidth = selectedSize.width;
    form.value.paperHeight = selectedSize.height;
    form.value.finishedSize = `${selectedSize.sizeName} (${selectedSize.width}×${selectedSize.height}mm)`;
    // 更新所有打印项的尺寸
    form.value.printList.forEach(item => {
      item.paperWidth = selectedSize.width;
      item.paperHeight = selectedSize.height;
    });
  }
}

/** 处理自定义尺寸变化 */
function handleCustomSizeChange() {
  if (form.value.paperWidth && form.value.paperHeight) {
    form.value.finishedSize = `${form.value.paperWidth}×${form.value.paperHeight}mm`;
    // 更新所有打印项的尺寸
    form.value.printList.forEach(item => {
      item.paperWidth = form.value.paperWidth;
      item.paperHeight = form.value.paperHeight;
      // 如果已经选择了纸张，检查尺寸
      if (item.makeupSize) {
        checkPaperSize(item);
      }
    });
  } else {
    form.value.finishedSize = null;
    // 清空所有打印项的尺寸
    form.value.printList.forEach(item => {
      item.paperWidth = null;
      item.paperHeight = null;
      item.sizeWarning = '';
    });
  }
}

/** 获取七牛云配置 */
async function getQiniuConfig() {
  try {
    const res = await getQiniuToken();
    if (res.code === 200) {
      qiniuToken.value = res.data.upToken;
      qiniuHost.value = res.data.host;
      qiniuBucket.value = res.data.bucket;
      qiniuFilePathPrefix.value = res.data.filePathPrefix;
    } else {
      throw new Error(res.msg || '获取上传配置失败');
    }
  } catch (error) {
    console.error('获取七牛云配置失败:', error);
    proxy.$modal.msgError(error.message || "获取七牛云配置失败");
  }
}

/** 更新打印列表 */
const updatePrintList = (newPrintList) => {
  form.value.printList = newPrintList;
};

// 在 script setup 中添加
const orderRef = ref(null);

// 添加初始化表单数据的函数
const initFormData = async () => {
  // 重置表单数据到初始状态
  form.value = {
    orderNo: null,
    customerUuid: null,
    customerName: null,
    contact: null,
    phone: null,
    addressUuid: null,
    address: null,
    isPickup: 'N',
    payType: null,
    priority: 0,
    finishedSizeType: 1,
    finishedSizeUuid: null,
    paperWidth: null,
    paperHeight: null,
    finishedSize: null,
    customerRequire: '',
    printList: [],
    craftList: [],
    orderNum: 1,
    salesUserId: null,
  };

  // 重置其他相关数据
  selectedProduct.value = null;
  discountList.value = [];
  selectedCustomer.value = null;

  try {
    // 获取客户信息
    await getCustomerList();
    // 获取拼版方案
    await getLayoutPatternList();
    // 获取纸张种类列表
    await getPaperClassList();
    // 获取成品尺寸列表
    await getFinishedSizeList();
    // 获取七牛云配置
    await getQiniuConfig();
    // 获取印刷型号列表
    await getPrinterTypeList();
    // 获取用户列表
    await getUserList();
    // 获取上传类型
    await getUploadType();
  } catch (error) {
    console.error('初始化数据失败:', error);
    proxy.$modal.msgError("初始化数据失败，请刷新页面重试");
  }

  // 清除store中的数据
  orderStore.clearOrderData();
};

// 添加一个标记，用于判断是否是首次进入页面
const isFirstEnter = ref(true);

// 添加 onMounted 钩子
onMounted(async () => {
  // 首次进入时初始化数据
  if (!orderStore.hasOrderData) {
    orderStore.clearOrderData();
    await initFormData();
    if (orderRef.value) {
      orderRef.value.resetFields();
    }
    // 重置字段后设置默认销售人员
    nextTick(() => {
      form.value.salesUserId = userStore.id;
    });
  }
});

// 添加 onActivated 钩子
onActivated(async () => {
  // 重置提交状态
  submitting.value = false;

  // 只有在以下情况才初始化数据：
  // 1. 首次进入页面且没有数据
  // 2. 从confirm页面返回且有数据需要恢复
  if (isFirstEnter.value && !orderStore.hasOrderData) {
    // 首次进入且没有数据时初始化
    isFirstEnter.value = false;
    await initFormData();
    if (orderRef.value) {
      orderRef.value.resetFields();
    }
    // 重置字段后设置默认销售人员
    nextTick(() => {
      form.value.salesUserId = userStore.id;
    });
  }
  // 如果从confirm页面返回，恢复数据
  else if (orderStore.hasOrderData) {
    // 从store中恢复数据
    form.value = orderStore.orderForm;
    selectedProduct.value = orderStore.selectedProduct;
    layouts.value = orderStore.layouts;
  }
  // 其他情况（如页签切换）保持当前状态，不做任何处理
});

// 添加 onDeactivated 钩子
onDeactivated(() => {
  // 组件失活时重置提交状态
  submitting.value = false;
});

// 删除工艺组
const handleDeleteCraft = (index) => {
  proxy.$modal.confirm('是否确认删除该工艺组？').then(() => {
    form.value.craftList.splice(index, 1);
  }).catch(() => { });
};

// 验证数字输入
const validateNumberInput = (value, craft) => {
  if (value === null || value === undefined) {
    craft.unitPrice = 0;
    return;
  }

  // 确保 unitPrice 是数字类型
  const newValue = parseFloat(value);
  if (!isNaN(newValue)) {
    // 获取对应工艺类型的折扣
    const craftTypeDiscount = discountList.value?.find(d => {
      // 根据工艺类型获取对应的折扣
      if (craft.craftType === '1') {
        // 装订工艺
        return d.craftClassType === '1';
      } else if (craft.craftType === '2') {
        // 印刷工艺
        return d.craftClassType === '2';
      } else if (craft.craftType === '3') {
        // 后期制作
        return d.craftClassType === '1';  // 后期制作使用装订工艺折扣
      } else {
        // 纸张单价折扣
        return d.craftClassType === '9999';
      }
    });

    // 如果没有原始价格，将当前价格设为原始价格（仅在第一次设置时执行）
    if (!craft.originalUnitPrice) {
      craft.originalUnitPrice = newValue;
    }

    // 更新当前价格
    craft.unitPrice = newValue;

    // 强制更新工艺组以触发视图更新
    const craftGroup = form.value.craftList.find(group => {
      return group.bindingList?.some(c => c.craftUuid === craft.craftUuid) ||
        group.laterMarkList?.some(c => c.craftUuid === craft.craftUuid);
    });

    if (craftGroup) {
      if (craftGroup.bindingList?.some(c => c.craftUuid === craft.craftUuid)) {
        craftGroup.bindingList = [...craftGroup.bindingList];
      }
      if (craftGroup.laterMarkList?.some(c => c.craftUuid === craft.craftUuid)) {
        craftGroup.laterMarkList = [...craftGroup.laterMarkList];
      }
    }
  }
};

// 处理价格更新
const handlePriceUpdate = (value, craft) => {
  validateNumberInput(value, craft);
};

// 计算签名数
const calculateSignatureCount = (index) => {
  const item = form.value.printList[index];

  // 1. 检查页数是否大于每贴页数
  if (item.pages && item.pagesPerSig) {
    if (item.pages <= item.pagesPerSig) {
      // 如果页数小于等于每贴页数，清空每贴页数
      item.pagesPerSig = null;
      return;
    }

    // 计算签名数并保存
    item.signatureCount = Math.ceil(item.pages / item.pagesPerSig);
  } else {
    item.signatureCount = 0;
  }
};

// 页数变化时的处理函数
const handlePagesChange = (index) => {
  const item = form.value.printList[index];

  // 当页数变化时，检查页数
  if (item.showPagesPerSig) {
    // 如果页数为空，则清空每贴页数
    if (!item.pages) {
      item.pagesPerSig = null;
      return;
    }

    // 如果页数小于16，清空每贴页数并提示
    if (item.pages < 16) {
      item.pagesPerSig = null;
      ElMessage({
        message: '页数小于16，无法设置每贴页数',
        type: 'warning'
      });
      return;
    }

    // 默认设置每贴页数为16
    if (!item.pagesPerSig) {
      item.pagesPerSig = 16;
      calculateSignatureCount(index);
    }
  }
};


// 判断是否为有理数且是循环小数
const isRepeatingDecimal = (numerator, denominator) => {
  // 先约分
  const gcd = (a, b) => b === 0 ? a : gcd(b, a % b);
  const commonDivisor = gcd(numerator, denominator);
  const reducedDenominator = denominator / commonDivisor;

  // 分解质因数，检查分母是否只包含2和5
  let d = reducedDenominator;
  while (d % 2 === 0) d /= 2;
  while (d % 5 === 0) d /= 5;

  return d !== 1;
};

// 获取贴数显示
const getSignatureDisplay = (pages, pagesPerSig) => {
  if (pages && pagesPerSig && pages > pagesPerSig) {
    const exactCount = pages / pagesPerSig;
    const isRepeating = isRepeatingDecimal(pages, pagesPerSig);

    // 如果是循环小数，显示6位小数
    // 否则显示2位小数（对于有限小数，如果小数位数不足2位，则显示实际位数）
    const formattedNumber = isRepeating ?
      exactCount.toFixed(6) :
      Number(exactCount.toFixed(2)).toString();

    return formattedNumber;
  }
  return '0';
};

/**
 * 处理文件上传完成事件
 * 在文件上传完成后，根据检测到的页数自动计算并设置每贴页数
 */
const handleFileUploaded = (data) => {
  // 确保数据有效
  if (!data || data.index === undefined || !data.pages || !data.fileName || !data.fileUrl) return;

  const item = form.value.printList[data.index];

  // 设置文件ID和文件名
  item.fileUrl = Array.isArray(data.fileUrl) ? data.fileUrl : [data.fileUrl];
  item.custFileUuid = data.fileUuid || null;
  item.custFileName = data.fileName;

  // 设置检测到的页数
  item.pages = data.pages;

  // 追加客户要求逻辑
  const currentRequire = form.value.customerRequire || '';
  if (currentRequire) {
    form.value.customerRequire = `${data.fileName}\n${currentRequire}`;
  } else {
    form.value.customerRequire = data.fileName;
  }

  // 确保页数已更新，然后调用页数变更处理函数
  nextTick(() => {
    handlePagesChange(data.index);
  });

  console.log('File uploaded successfully:', {
    index: data.index,
    custFileUuid: item.custFileUuid,
    custFileName: item.custFileName,
    pages: item.pages
  });
};

/**
 * 处理文件删除事件
 * 从客户要求中删除文件名，保留其他内容
 */
const handleFileRemoved = (data) => {
  if (!data || data.fileName === undefined || data.index === undefined) return;

  form.value.printList[data.index].fileUrl = [];
  form.value.printList[data.index].custFileUuid = null;
  form.value.printList[data.index].custFileName = null;
  form.value.printList[data.index].pages = null;
  // 客户要求字段移除文件名
  const currentRequire = form.value.customerRequire || '';
  const lines = currentRequire.split('\n');
  const newLines = lines.map(line => {
    if (line.trim() === data.fileName.trim()) {
      return null;
    }
    return line.replace(data.fileName, '').trim();
  }).filter(line => line !== null && line !== '');
  form.value.customerRequire = newLines.join('\n');

  console.log('File removed:', {
    index: data.index,
    fileName: data.fileName
  });
};

// 获取选中的工艺
const getSelectedCrafts = (craftList) => {
  const selectedCrafts = craftList
    .filter(craft => craft.printCraftChecked === 'Y')
    .map(craft => craft.craftName);

  if (selectedCrafts.length === 0) return '';
  if (selectedCrafts.length <= 5) {
    return selectedCrafts.join('、');
  }
  return `${selectedCrafts[0]}、${selectedCrafts[1]} 等${selectedCrafts.length}项`;
};

// 添加获取选中装订工艺的方法
const getSelectedBinding = (bindingList) => {
  const selectedBinding = bindingList.find(craft => craft.printCraftChecked === 'Y');
  return selectedBinding ? selectedBinding.craftName : '';
};

// 添加获取选中后期制作的方法
const getSelectedLaterMark = (laterMarkList) => {
  const selectedCrafts = laterMarkList
    .filter(craft => craft.printCraftChecked === 'Y')
    .map(craft => craft.craftName);

  if (selectedCrafts.length === 0) return '';
  if (selectedCrafts.length <= 5) {
    return selectedCrafts.join('、');
  }
  return `${selectedCrafts[0]}、${selectedCrafts[1]} 等${selectedCrafts.length}项`;
};

/** 处理自提变化 */
const handlePickupChange = (value) => {
  if (value === 'Y') {
    // 如果选择自提，清空地址相关字段
    form.value.addressUuid = null;
    form.value.address = null;
  }
};

/** 获取用户列表 */
async function getUserList() {
  try {
    const res = await getAllUserList();
    userList.value = res.data || [];
  } catch (error) {
    proxy.$modal.msgError("获取用户列表失败");
  }
}

/** 获取印刷型号列表 */
async function getPrinterTypeList() {
  try {
    const res = await listPrinterType({ pageSize: 9999 });
    printerTypeList.value = res.rows || [];
  } catch (error) {
    proxy.$modal.msgError("获取印刷型号列表失败");
  }
}

/** 处理印刷机型号选择变化 */
const handlePrinterTypeChange = (printerTypeId, index) => {
  const item = form.value.printList[index];
  if (!printerTypeId) {
    item.printerType = null;
    return;
  }

  const selectedPrinter = printerTypeList.value.find(p => p.printerTypeId === printerTypeId);
  if (selectedPrinter) {
    item.printerType = selectedPrinter.printerType;
  }
};

// 获取上传类型
const getUploadType = async () => {
  try {
    const res = await getBusinessUploadType()
    if (res.code === 200) {
      uploadType.value = res.data.userType
      uploadPath.value = res.data.uploadPath || ''
      console.log('uploadType', uploadType.value)
      console.log('uploadPath', uploadPath.value)
    } else {
      ElMessage.error(res.userType || '获取上传类型失败')
    }
  } catch (error) {
    console.error('获取上传类型失败:', error)
    ElMessage.error('获取上传类型失败')
  }
}

// 根据折扣率返回不同的标签类型
const getDiscountRateType = (rate) => {
  const percentage = rate * 100;
  if (percentage >= 95) return 'primary';
  if (percentage >= 90) return 'success';
  if (percentage >= 80) return 'warning';
  return 'danger';
};

// 判断是否是匹配当前订单份数的折扣
const isMatchingDiscount = (discount) => {
  if (!form.value.orderNum) return false;
  const orderNum = form.value.orderNum;
  if (!discount.maxQuantity) {
    return orderNum >= discount.minQuantity;
  }
  return orderNum >= discount.minQuantity && orderNum <= discount.maxQuantity;
};

// 获取工艺类型名称
const getCraftTypeName = (craftClassType) => {
  if (craftClassType === '9999') return '印刷';
  return m207.value?.find(dict => dict.value === craftClassType)?.label || '未知工艺';
};

/** 处理印刷工艺变化 */
const handlePrintCraftChange = (craftUuid, craft) => {
  // 保存原始单价（如果还没有保存过）
  if (!craft.originalUnitPrice) {
    craft.originalUnitPrice = craft.unitPrice;
  }

  if (craft.printCraftChecked === 'Y') {
    // 获取印刷工艺折扣（craftClassType: '2'）
    const printCraftDiscount = discountList.value?.find(d => d.craftClassType === '2');
    console.log('printCraftDiscount', printCraftDiscount);
    if (printCraftDiscount && printCraftDiscount.discountRate) {
      // 应用折扣
      craft.unitPrice = parseFloat((craft.originalUnitPrice * printCraftDiscount.discountRate).toFixed(2));
    } else {
      craft.unitPrice = craft.originalUnitPrice;
    }

  } else {

    // 取消选择时恢复原始价格
    craft.unitPrice = craft.originalUnitPrice;
  }
};

/** 处理装订工艺变化 */
const handleBindingChange = (craft) => {
  // 保存原始单价
  if (!craft.originalUnitPrice) {
    craft.originalUnitPrice = craft.unitPrice;
  }

  // 设置选中状态
  craft.printCraftChecked = 'Y';

  // 装订工艺折扣（craftClassType: '1'）
  const bindingDiscount = discountList.value?.find(d => d.craftClassType === '1');
  if (bindingDiscount && bindingDiscount.discountRate) {
    craft.unitPrice = parseFloat((craft.originalUnitPrice * bindingDiscount.discountRate).toFixed(2));
  }

  // 取消其他装订工艺的选中状态
  const craftGroup = form.value.craftList.find(group =>
    group.bindingList?.some(c => c.craftUuid === craft.craftUuid)
  );
  if (craftGroup) {
    craftGroup.bindingList.forEach(c => {
      if (c.craftUuid !== craft.craftUuid) {
        c.printCraftChecked = 'N';
        // 恢复原始价格
        c.unitPrice = c.originalUnitPrice || c.unitPrice;
      } else {
        // 确保选中的工艺的printCraftChecked为Y
        c.printCraftChecked = 'Y';
      }
    });
    // 更新selectedCraftUuid
    craftGroup.selectedCraftUuid = craft.craftUuid;
  }
};

/** 处理后期制作工艺变化 */
const handleLaterMarkChange = (craft) => {
  // 保存原始单价
  if (!craft.originalUnitPrice) {
    craft.originalUnitPrice = craft.unitPrice;
  }

  // 直接设置选中状态（不需要手动切换，因为v-model已经处理了）
  if (craft.printCraftChecked === 'Y') {
    // 选中时应用折扣
    const laterMarkDiscount = discountList.value?.find(d => d.craftClassType === '1');
    if (laterMarkDiscount && laterMarkDiscount.discountRate) {
      craft.unitPrice = parseFloat((craft.originalUnitPrice * laterMarkDiscount.discountRate).toFixed(2));
    }
  } else {
    // 取消选择时恢复原始价格
    craft.unitPrice = craft.originalUnitPrice || craft.unitPrice;
  }

  // 找到对应的工艺组并强制更新
  const craftGroup = form.value.craftList.find(group =>
    group.laterMarkList?.some(c => c.craftUuid === craft.craftUuid)
  );

  if (craftGroup) {
    // 强制更新工艺组
    craftGroup.laterMarkList = [...craftGroup.laterMarkList];
  }

  console.log('后期制作选择变更后:', {
    craftName: craft.craftName,
    craftUuid: craft.craftUuid,
    unitPrice: craft.unitPrice,
    printCraftChecked: craft.printCraftChecked,
    craftGroup: craftGroup ? {
      laterMarkList: craftGroup.laterMarkList.map(c => ({
        craftName: c.craftName,
        craftUuid: c.craftUuid,
        printCraftChecked: c.printCraftChecked
      }))
    } : null
  });
};

// 应用所有工艺折扣的函数
const applyAllCraftsDiscount = () => {
  if (!form.value.craftList) return;

  form.value.craftList.forEach(group => {
    // 处理印刷工艺折扣
    if (group.printCraftList) {
      group.printCraftList.forEach(craft => {
        if (craft.printCraftChecked === 'Y') {
          // 保存原始单价
          if (!craft.originalUnitPrice) {
            craft.originalUnitPrice = craft.unitPrice;
          }

          // 获取印刷工艺折扣（craftClassType: '2'）
          const printCraftDiscount = discountList.value?.find(d => d.craftClassType === '2');
          if (printCraftDiscount && printCraftDiscount.discountRate) {
            // 应用折扣
            craft.unitPrice = parseFloat((craft.originalUnitPrice * printCraftDiscount.discountRate).toFixed(2));
          }
        }
      });
    }

    // 处理装订工艺折扣
    if (group.bindingList) {
      group.bindingList.forEach(craft => {
        if (craft.printCraftChecked === 'Y') {
          // 保存原始单价
          if (!craft.originalUnitPrice) {
            craft.originalUnitPrice = craft.unitPrice;
          }

          // 获取装订工艺折扣（craftClassType: '1'）
          const bindingDiscount = discountList.value?.find(d => d.craftClassType === '1');
          if (bindingDiscount && bindingDiscount.discountRate) {
            // 应用折扣
            craft.unitPrice = parseFloat((craft.originalUnitPrice * bindingDiscount.discountRate).toFixed(2));
          }
        }
      });
    }

    // 处理后期制作工艺折扣
    if (group.laterMarkList) {
      group.laterMarkList.forEach(craft => {
        if (craft.printCraftChecked === 'Y') {
          // 保存原始单价
          if (!craft.originalUnitPrice) {
            craft.originalUnitPrice = craft.unitPrice;
          }

          // 后期制作使用装订工艺折扣（craftClassType: '1'）
          const laterMarkDiscount = discountList.value?.find(d => d.craftClassType === '1');
          if (laterMarkDiscount && laterMarkDiscount.discountRate) {
            // 应用折扣
            craft.unitPrice = parseFloat((craft.originalUnitPrice * laterMarkDiscount.discountRate).toFixed(2));
          }
        }
      });
    }
  });
};

// 监听折扣列表变化，重新应用所有折扣
watch(discountList, () => {
  applyAllCraftsDiscount();
}, { deep: true });

// 在获取产品数据后初始化折扣
watch(() => form.value.craftList, (newVal) => {
  if (newVal && discountList.value) {
    applyAllCraftsDiscount();
  }
}, { immediate: true });

// 监听表单变化并同步到 store
watch(() => form.value, (newForm) => {
  if (newForm) {
    orderStore.saveOrderData({
      orderForm: newForm,
      selectedProduct: selectedProduct.value,
      layouts: null // 这里的 layouts 根据实际情况设置
    });
  }
}, { deep: true });

// 选择共享文件后的处理（替换模式，只能选择一个文件）
function handleSharedFileSelect(file, index) {
  const item = form.value.printList[index]
  console.log('file', file)

  // 如果之前已经有文件，提示用户这是替换操作
  if (item.sharedFile) {
    ElMessage.info(`已替换文件：${item.sharedFile.label} → ${file.label}`)
  }

  // 替换文件信息
  item.sharedFile = file
  item.sharedDialogVisible = false
  // 赋值文件路径和文件名，便于后续统一处理
  item.fileUrl = [file.fullPath]
  item.custFileUuid = file.id // 赋值id，确认页面会根据id处理是否直接拼板
  item.custFileName = file.label
  item.originalFileName = file.label
  console.log('item', item)
}

</script>

<style lang="scss" scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;

  // 基本信息表单优化
  .el-form {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    &:hover {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #303133;
      font-size: 14px;
    }

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-select__wrapper) {
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      &.is-focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-textarea__inner) {
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      &:focus {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    :deep(.el-radio-group) {
      .el-radio {
        margin-right: 24px;

        .el-radio__label {
          font-weight: 500;
        }
      }
    }

    :deep(.el-input-number) {
      .el-input__wrapper {
        border-radius: 8px;
      }
    }
  }

  .box-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 18px;
      font-weight: bold;
    }
  }
}

// 文件上传区域优化
.file-upload-container {
  background: #fafbfc;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409EFF;
    background: rgba(64, 158, 255, 0.02);
  }
}

.upload-mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;

  .mode-indicator {
    display: flex;
    align-items: center;
    gap: 8px;

    .mode-text {
      font-weight: 600;
      color: #303133;
      font-size: 14px;
    }
  }

  .mode-tag {
    font-weight: 500;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      max-width: none;
      white-space: normal;
      word-break: break-all;
    }
  }
}

.upload-content-area {
  min-height: 80px;
}

.shared-upload-section {
  .upload-action-area {
    text-align: center;
    margin-bottom: 16px;

    .select-file-button {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
      }
    }
  }

  .selected-file-display {
    .file-info-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: white;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border-color: #c0c4cc;
      }

      .file-icon {
        width: 40px;
        height: 40px;
        background: rgba(64, 158, 255, 0.1);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .file-details {
        flex: 1;
        min-width: 0;

        .file-name {
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-path {
          font-size: 12px;
          color: #909399;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .file-status {
        flex-shrink: 0;
      }
    }
  }
}

.direct-upload-section {
  :deep(.pdf-upload-container) {
    background: transparent;
    border: none;
    padding: 0;
  }
}

.empty-product {
  padding: 40px;
  text-align: center;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0;
  }
}

.detail-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #fff;

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #EBEEF5;

    .title {
      font-size: 16px;
      font-weight: bold;
    }

    .delete-btn {
      margin-left: auto;
    }
  }
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  margin: 0 0 15px;
  color: #606266;
}

.basic-info,
.imposition-settings {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;

  :deep(.el-form-item) {
    margin-bottom: 15px;
  }

  :deep(.el-radio-group) {
    display: flex;
    gap: 20px;
  }

  :deep(.el-radio) {
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.unit {
  margin-left: 8px;
  color: #606266;
  font-size: 12px;
}

:deep(.el-select) {
  width: 100%;
}

.craft-section {
  margin-top: 20px;

  .craft-group {
    margin-bottom: 20px;
    padding: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    &:last-child {
      margin-bottom: 0;
    }

    .craft-group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #EBEEF5;

      .group-title {
        font-size: 15px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .info-section {
    // This is for Print Crafts, ensure similar structure if used in add.vue
    height: 100%;
    margin: 0;
    background-color: #fff;
    border: 1px solid #EBEEF5;

    .section-title {
      padding: 12px 16px;
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      border-bottom: 1px solid #EBEEF5;
      background-color: #f8f9fa;
    }

    :deep(.el-table) {
      .el-table__cell {
        padding: 8px;
      }
    }
  }
}

.craft-card {
  margin: 0;
  border: none;

  :deep(.el-card__body) {
    padding: 12px;
  }

  .craft-list {
    .craft-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .craft-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        padding: 10px 15px;
        background: #f8f9fa;
        border-radius: 4px;

        :deep(.el-radio) {
          margin-right: 0;
          height: 32px;

          .el-radio__label {
            padding-left: 6px;
          }
        }

        :deep(.el-checkbox) {
          margin-right: 0;
          height: 32px;

          .el-checkbox__label {
            padding-left: 6px;
          }
        }

        .craft-input {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 200px;

          .input-group {
            display: flex;
            align-items: center;
            gap: 4px;
          }

          .price-info {
            color: #909399;
            font-size: 13px;
          }
        }
      }

      // Closing brace for .craft-content
    }

    // Closing brace for .craft-item
  }

  // Closing brace for .craft-list
}

// Closing brace for .craft-card

.collapse-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 100%;
  overflow: hidden;

  .collapse-title {
    font-size: 15px;
    font-weight: 500;
    color: #303133;
    flex-shrink: 0;
  }

  .selected-crafts {
    color: #409EFF;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
  }
}

:deep(.el-collapse-item__header) {
  padding: 8px 12px;
  font-size: 14px;
  background-color: #f8f9fa;
  overflow: hidden;

  &.is-active {
    border-bottom-color: #f0f0f0;
  }
}

:deep(.el-collapse-item__content) {
  padding: 0;
}

:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}

.price-text {
  color: #409EFF;
  font-weight: 500;
}

.disabled-text {
  color: #c0c4cc;
}

.must-tag {
  font-size: 12px;
  color: #f56c6c;
  margin-left: 4px;
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 4px;

  .unit-text {
    color: #606266;
    font-size: 13px;
  }
}

:deep(.el-checkbox) {
  height: auto;
  margin-right: 0;
}

.imposition-section {
  margin-top: 24px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 15px;
    font-weight: 600;
    padding: 14px 16px;
    background: #f7f9fc;
    border-bottom: 1px solid #ebeef5;
    color: #303133;
  }

  .imposition-content {
    padding: 16px;

    .layout-option {
      margin-bottom: 20px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      background: #fff;
      transition: all 0.3s;

      &:hover {
        border-color: #409EFF;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      .info-header {
        display: flex;
        align-items: center;
        gap: 24px;
        padding-bottom: 12px;
        margin-bottom: 12px;
        border-bottom: 1px dashed #e4e7ed;

        .header-item {
          display: flex;
          align-items: center;
          color: #606266;

          .value {
            font-weight: 500;
            color: #303133;
            margin-right: 4px;
          }

          .label {
            color: #909399;
            font-size: 13px;
          }
        }
      }

      .special-layout,
      .regular-layout {
        .layout-radio-group {
          margin-bottom: 16px;
          width: 100%;

          .el-radio {
            width: 100%;
            margin-right: 0;
            margin-bottom: 10px;
            padding: 10px 16px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            transition: background-color 0.2s;

            &.is-checked {
              border-color: #409EFF;
              background-color: #ecf5ff;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .layout-details {
        padding: 12px;
        margin-bottom: 16px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px dashed #dcdfe6;

        .layout-detail-item {
          padding: 8px 0;

          &:not(:last-child) {
            border-bottom: 1px dashed #ebeef5;
          }

          .detail-row {
            display: flex;
            align-items: center;
            margin-bottom: 4px;

            &:last-child {
              margin-bottom: 0;
            }

            .detail-label {
              width: 80px;
              color: #909399;
              font-size: 13px;
            }

            .detail-value {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }

      .layout-part {
        .part-content {
          .content-row {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 16px;
            margin-bottom: 8px;

            .content-item {
              display: flex;
              align-items: center;
              color: #606266;
              min-width: 120px;

              .label {
                color: #909399;
                font-size: 13px;
                margin-right: 4px;
              }

              .value {
                color: #303133;
                font-weight: 500;

                &.price {
                  color: #f56c6c;
                }
              }
            }
          }
        }
      }

      .price-info {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px dashed #e4e7ed;

        .price-row {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 24px;

          .price-item {
            display: flex;
            align-items: center;
            white-space: nowrap;

            .label {
              color: #909399;
              font-size: 13px;
              margin-right: 4px;
            }

            .value {
              color: #303133;
              font-weight: 500;

              &.price {
                color: #f56c6c;
                font-size: 15px;
              }
            }
          }
        }
      }
    }

    .price-details {
      margin-top: 20px;
      padding: 16px;
      background: #fff7f7;
      border-radius: 4px;
      border: 1px dashed #f56c6c;

      .price-title {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #303133;
      }

      .price-desc {
        color: #f56c6c;
        font-size: 14px;
        line-height: 1.6;
      }
    }
  }
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  :deep(.el-form-item__content) {
    margin-left: 0 !important;
    justify-content: flex-end;
  }
}

.signature-count {
  display: inline-block;
  margin-left: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  white-space: nowrap;

  &.rational {
    color: #67C23A;
    /* 有理数绿色 */
    background-color: rgba(103, 194, 58, 0.15);
  }

  &.irrational {
    color: #F56C6C;
    /* 无理数红色 */
    background-color: rgba(245, 108, 108, 0.1);
  }
}

.pagesPerSig-container {
  display: flex;
  align-items: center;
  width: 100%;

  :deep(.el-select) {
    width: 120px; // 设置固定宽度
  }
}

.option-signature-count {
  float: right;
  font-size: 13px;
  min-width: 55px;
  text-align: right;
  padding-left: 8px;
  color: #67C23A;
}

.option-warning {
  float: right;
  color: #E6A23C;
  font-size: 13px;
  margin-left: 8px;
}

.pages-warning {
  color: #E6A23C;
  font-size: 13px;
  margin-left: 10px;
}

.selected-signature-count {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 13px;
  min-width: 55px;
  justify-content: center;
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}

// 修改下拉选项的样式
:deep(.el-select-dropdown__item) {
  padding: 0 12px;
  height: 32px;
  line-height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span:first-child {
    flex: 0 0 auto;
    margin-right: 8px;
  }
}

.check-mark {
  color: #67C23A;
}

:deep(.el-switch) {
  --el-switch-on-color: #67C23A;
}

:deep(.el-switch.is-checked .el-switch__core) {
  border-color: #67C23A;
}

.unit-text {
  color: #606266;
  font-size: 13px;
  margin-left: 4px;
}

.craft-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .craft-price {
    color: #409EFF;
    font-weight: 500;
    margin-left: 16px;
  }
}

.craft-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;

  .craft-info-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      width: 80px;
      color: #909399;
      flex-shrink: 0;
    }

    .info-value {
      color: #303133;
      flex: 1;
    }
  }
}

:deep(.el-collapse-item) {
  &.is-disabled {
    .el-collapse-item__header {
      background-color: #ecf5ff;
      border-bottom-color: transparent;
    }
  }

  .el-collapse-item__header {
    padding: 8px 16px;
  }

  .el-collapse-item__content {
    padding: 12px;
  }
}

.craft-info {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;

  .info-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      color: #909399;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      flex: 1;
    }
  }
}

:deep(.el-checkbox) {
  margin-right: 0;
}

.collapse-title {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  padding: 12px 16px;
}

:deep(.el-collapse-item__content) {
  padding: 16px;
}

.collapse-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 100%;

  .collapse-title {
    flex-shrink: 0;
  }

  .selected-crafts {
    color: #409EFF;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
  }
}

:deep(.el-collapse-item__header) {
  padding: 8px 16px;
  overflow: hidden;
}

:deep(.el-collapse-item__content) {
  padding: 16px;
}

.craft-card {
  margin: 0;
  border: none;

  :deep(.el-card__body) {
    padding: 12px;
  }
}

.price-input {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  :deep(.el-input-number) {
    .el-input__wrapper {
      padding: 0 8px;
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      width: 24px;
    }
  }
}

.printer-type-settings {
  :deep(.el-form-item) {
    margin-bottom: 15px;
  }

  :deep(.el-select) {
    width: 100%;
  }
}

.margin-info {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
  padding: 2px 4px;
  border-radius: 4px;
  background-color: #f0f2f5;

  .el-icon {
    font-size: 12px;
    margin: 0 1px;
  }
}

.paper-count {
  float: right;
  color: #131314;
  font-size: 13px;
  margin-left: 8px;
}

.size-warning {
  font-size: 12px;
  color: #E6A23C;
  margin-top: 4px;
  line-height: 1.2;
}

.paper-price {
  margin-top: 8px;
  font-size: 13px;
  color: #606266;

  .price {
    color: #f56c6c;
    font-weight: 500;
    font-size: 15px;
  }
}

.discount-info {
  padding: 0;

  :deep(.el-table) {
    margin-top: 8px;
  }
}

.discount-info-content {
  padding: 0;

  :deep(.el-table) {
    margin-top: 8px;
  }
}

.order-num-container {
  display: flex;
  align-items: center;
}

.matched-discount {
  margin-left: 8px;
  font-size: 13px;
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.price-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  min-height: 24px;

  .original-price {
    text-decoration: line-through;
    color: #909399;
    margin-right: 8px;
    font-size: 13px;
  }

  .discount-tag {
    margin-right: 8px;
    transform: scale(0.9);
  }
}

.input-group {
  display: flex;
  flex-direction: column;

  .el-input-number {
    margin-bottom: 4px;
  }
}

.price-row {
  display: flex;
  align-items: center;
  gap: 8px;

  .original-price {
    text-decoration: line-through;
    color: #909399;
    font-size: 13px;
  }

  .discount-tag {
    transform: scale(0.9);
  }

  .unit-text {
    color: #606266;
  }
}

// 移除或更新之前的相关样式
.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
