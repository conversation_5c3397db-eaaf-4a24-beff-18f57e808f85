<template>
  <el-dialog
    v-model="dialogVisible"
    title="结算单"
    width="80%"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <div class="settlement-container">
      <!-- 结算单头部 -->
      <div class="settlement-header">
        <h2 class="settlement-title">结 算 单</h2>
      </div>

      <!-- 客户和订单基本信息 -->
      <div class="basic-info">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">客户名称:</span>
              <span class="value">{{ settlementData.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">联系人/电话:</span>
              <span class="value">{{ contactInfo }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">配送地址:</span>
              <span class="value">{{ settlementData.address }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">交货时间:</span>
              <span class="value">{{ formatDeliveryTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">订单份数:</span>
              <span class="value">{{ settlementData.orderNum || 1 }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="24">
            <div class="info-item">
              <span class="label">客户要求:</span>
              <span class="value">{{ settlementData.customerRequire || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 产品明细表格 -->
      <div class="product-list">
        <h3>产品明细</h3>
        <el-table :data="productList" border stripe class="settlement-table">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="partName" label="部件名称" width="120" align="center" />
          <el-table-column prop="itemName" label="项目名称" min-width="200" />
          <el-table-column prop="copies" label="份数" width="80" align="center" />
          <el-table-column prop="pages" label="P数" width="80" align="center">
            <template #default="{ row }">
              {{ row.pages && row.pages !== '' ? row.pages : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="totalCount" label="总数" width="100" align="center" />
          <el-table-column prop="unitPrice" label="单价" width="100" align="right">
            <template #default="{ row }">
              {{ Number(row.unitPrice || 0).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="小计" width="120" align="right" class-name="amount-column">
            <template #default="{ row }">
              ¥{{ Number(row.amount || 0).toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 费用汇总 -->
      <div class="cost-summary">
        <h3>费用汇总</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="总金额">
            <span class="amount">¥{{ orderAmount }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
<!--        <el-button type="primary" @click="handlePrint">打印结算单</el-button>-->
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = ref(false)
const settlementData = ref({})

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.data) {
    // 简单地复制数据，避免复杂的处理
    settlementData.value = { ...props.data }
  }
})

// 监听对话框关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 联系信息
const contactInfo = computed(() => {
  const contact = settlementData.value.contact || '-'
  const phone = settlementData.value.phone || '-'
  return `${contact} ${phone}`
})

// 交货时间格式化
const formatDeliveryTime = computed(() => {
  if (settlementData.value.deliveryTime) {
    const date = new Date(settlementData.value.deliveryTime)
    return date.toLocaleString('zh-CN').replace(/\//g, '-')
  }
  return ''
})

// 成品尺寸
const finishedSize = computed(() => {
  return settlementData.value.finishedSize || ''
})

// 产品列表 - 按照结算单格式
const productList = computed(() => {
  const list = []
  const orderNum = settlementData.value.orderNum || 100

  // 处理印刷产品
  if (settlementData.value.printList && Array.isArray(settlementData.value.printList)) {
    settlementData.value.printList.forEach((printItem, index) => {
      // 1. 印刷费用 - 根据拼版方案计算
      const printAmount = calculatePrintAmount(printItem)

      // 总是添加印刷项目，确保数据格式正确
      list.push({
        partName: printItem.partName || '封面',
        itemName: getPrintItemDescription(printItem),
        copies: orderNum,
        pages: printItem.pages || 1,
        totalCount: getTotalCount(printItem, orderNum),
        unitPrice: Number(printItem.printUnitPrice || 0).toFixed(2),
        amount: Number(printAmount || 0).toFixed(2)
      })

      // 2. 印刷工艺 - 只显示选中的工艺
      if (printItem.printCraftList && Array.isArray(printItem.printCraftList)) {
        printItem.printCraftList.forEach((craft, craftIndex) => {
          if (craft.printCraftChecked === 'Y') {
            // 后期制作直接使用已计算的金额
            const craftAmount = craft.craftAmount || 0

            const craftItem = {
              partName: printItem.partName || '封面',
              itemName: craft.craftName || '工艺',
              copies: orderNum,
              pages: '', // 工艺项目不显示P数
              totalCount: '', // 后期制作的总数不显示
              unitPrice: Number(craft.unitPrice || 0).toFixed(2),
              amount: Number(craftAmount || 0).toFixed(2)
            }
            list.push(craftItem)
          }
        })
      }
    })
  }

  // 处理装订工艺
  if (settlementData.value.craftList && Array.isArray(settlementData.value.craftList)) {
    settlementData.value.craftList.forEach((craftGroup, index) => {
      // 处理装订工艺
      if (craftGroup.bindingList && Array.isArray(craftGroup.bindingList)) {
        craftGroup.bindingList.forEach((binding, bindingIndex) => {
          if (binding.printCraftChecked === 'Y') {
            // 装订工艺金额 = 单价 * 订单数量
            const unitPrice = Number(binding.unitPrice || 0)
            const bindingAmount = unitPrice * orderNum

            const bindingItem = {
              partName: craftGroup.partName || '装订',
              itemName: `${craftGroup.partName} - ${binding.craftName}`,
              copies: orderNum,
              pages: '', // 装订不显示P数
              totalCount: orderNum,
              unitPrice: Number(binding.unitPrice || 0).toFixed(2),
              amount: Number(bindingAmount || 0).toFixed(2)
            }
            list.push(bindingItem)
          }
        })
      }

      // 处理后期制作
      if (craftGroup.laterMarkList && Array.isArray(craftGroup.laterMarkList)) {
        craftGroup.laterMarkList.forEach((later, laterIndex) => {
          if (later.printCraftChecked === 'Y') {
            // 后期制作金额 = 单价 * 订单数量
            const unitPrice = Number(later.unitPrice || 0)
            const laterAmount = unitPrice * orderNum

            const laterItem = {
              partName: craftGroup.partName || '后期制作',
              itemName: later.craftName || '后期制作',
              copies: orderNum,
              pages: '', // 后期制作不显示P数
              totalCount: '', // 后期制作的总数不显示
              unitPrice: Number(later.unitPrice || 0).toFixed(2),
              amount: Number(laterAmount || 0).toFixed(2)
            }
            list.push(laterItem)
          }
        })
      }
    })
  }

  return list
})

// 获取印刷项目描述
const getPrintItemDescription = (item) => {
  const parts = []

  // 纸张信息
  if (item.paperName) {
    parts.push(item.paperName)
  }

  // 纸张尺寸
  if (item.paperWidth && item.paperHeight) {
    parts.push(`${item.paperWidth}×${item.paperHeight}mm`)
  }

  // 印刷方式 - 根据颜色和单双面判断
  const printType = getPrintType(item)
  if (printType) {
    parts.push(printType)
  }

  // 拼版信息
  if (item.isMakeup === 'Y') {
    parts.push('拼版印刷')
  }

  return parts.length > 0 ? parts.join(' ') : '印刷'
}

// 获取印刷方式描述
const getPrintType = (item) => {
  const isColor = item.isColorPrint === 1 || item.isColorPrint === '1'
  const isSingle = item.isSinglePrint === 1 || item.isSinglePrint === '1'

  if (isColor && isSingle) {
    return '彩色单面'
  } else if (isColor && !isSingle) {
    return '彩色双面'
  } else if (!isColor && isSingle) {
    return '黑白单面'
  } else if (!isColor && !isSingle) {
    return '黑白双面'
  }

  return '印刷'
}

// 计算总数
const getTotalCount = (item, orderNum) => {
  if (item.isMakeup === 'Y' && item.makeupPageLayout) {
    // 拼版情况：使用拼版数量
    return item.makeupPageLayout.pageLayoutQty || 0
  } else {
    // 不拼版情况：P数 × 订单份数
    return (item.pages || 1) * orderNum
  }
}

// 计算印刷金额 - 根据拼版方案
const calculatePrintAmount = (printItem) => {
  try {
    const orderNum = settlementData.value.orderNum || 1

    // 计算印刷费用 - 与 confirm.vue 保持一致
    if (printItem.isMakeup === 'Y' && printItem.makeupPageLayout) {
      // 拼版方案的总价
      return Number(printItem.makeupPageLayout.pageLayoutTotalAmount || 0)
    } else {
      // 不拼版的总价 = 单价 * 页数 * 份数
      const unitPrice = Number(printItem.printUnitPrice || 0)
      const pages = Number(printItem.pages || 0)
      return unitPrice * pages * orderNum
    }
  } catch (error) {
    console.error('计算印刷金额出错:', error)
    return 0
  }
}

// 总金额 - 直接使用 orderAmount
const orderAmount = computed(() => {
  return Number(settlementData.value.orderAmount || 0).toFixed(2)
})


// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handlePrint = () => {
  // 使用CSS媒体查询来控制打印内容
  window.print()
}
</script>

<style scoped>
.settlement-container {
  padding: 20px;
  background: white;
  font-family: "Microsoft YaHei", "SimSun", serif;
}

.settlement-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 20px;
}

.settlement-title {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin: 0;
  letter-spacing: 4px;
}

.basic-info {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label {
  font-weight: bold;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.value {
  color: #303133;
  flex: 1;
}

.product-list,
.cost-summary {
  margin-bottom: 30px;
}

.product-list h3,
.cost-summary h3 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 15px;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.settlement-table {
  font-size: 14px;
}

.settlement-table :deep(.el-table__header) {
  background-color: #f5f7fa;
}

.settlement-table :deep(.el-table__header th) {
  background-color: #f5f7fa !important;
  font-weight: bold;
  color: #303133;
}

.settlement-table :deep(.amount-column) {
  color: #e6a23c;
  font-weight: bold;
}

.amount {
  font-weight: bold;
  font-size: 16px;
}

.amount.discount {
  color: #f56c6c;
}

.amount.final {
  color: #67c23a;
  font-size: 18px;
}

.dialog-footer {
  text-align: right;
}

@media print {
  /* 隐藏所有元素 */
  * {
    visibility: hidden !important;
  }

  /* 只显示结算单容器及其子元素 */
  .settlement-container,
  .settlement-container * {
    visibility: visible !important;
  }

  /* 隐藏对话框相关元素 */
  .el-dialog__wrapper {
    background: transparent !important;
  }

  .el-dialog {
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
  }

  .el-dialog__header,
  .dialog-footer {
    display: none !important;
  }

  /* 结算单样式 */
  .settlement-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: auto !important;
    padding: 15px !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    font-size: 12px !important;
    background: white !important;
  }

  .settlement-title {
    font-size: 20px !important;
    margin-bottom: 20px !important;
  }

  .settlement-table {
    font-size: 11px !important;
  }

  .basic-info {
    background: white !important;
    padding: 10px !important;
    margin-bottom: 15px !important;
  }

  /* 确保表格正常显示 */
  .el-table,
  .el-descriptions {
    font-size: 11px !important;
  }
}
</style>
