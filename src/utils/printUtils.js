/**
 * 打印工具类 - 生产版本
 * 专注于A5横版打印功能
 */

/**
 * 新窗口打印 - A5横版专用
 */
export function printInNewWindow(content, options = {}) {
  const title = options.title || '打印预览'
  
  // 创建新窗口
  const printWindow = window.open('', '_blank', 'width=900,height=700')
  if (!printWindow) {
    alert('无法打开预览窗口，请检查浏览器弹窗设置')
    return
  }

  // 构建HTML内容
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${title}</title>
      <style>
        @page {
          size: A5 landscape;
          margin: 5mm;
        }
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: "Microsoft YaHei", "SimSun", serif;
          font-size: 6px;
          line-height: 1.0;
          color: #000;
          background: white;
          padding: 3mm;
        }
        
        .work-order-header {
          margin-bottom: 2mm;
          padding-bottom: 1mm;
          border-bottom: 1px solid #000;
          text-align: center;
        }
        
        .company-name {
          font-weight: bold;
          margin: 0 0 1mm 0;
          font-size: 8px;
        }
        
        .document-title {
          font-weight: bold;
          margin: 0;
          font-size: 6px;
        }
        
        .basic-info-with-qr {
          display: flex;
          margin-bottom: 2mm;
          gap: 8px;
          align-items: flex-start;
        }
        
        .basic-info {
          flex: 1;
          margin-bottom: 1mm;
        }
        
        .info-row {
          margin-bottom: 1mm;
        }
        
        .info-item {
          display: inline-block;
          margin-right: 5mm;
          vertical-align: top;
        }
        
        .info-item.full-width {
          display: block;
          margin-right: 0;
          margin-bottom: 1mm;
        }
        
        .label {
          font-weight: bold;
          margin-right: 1mm;
        }
        
        .value {
          color: #000;
        }
        
        .order-no {
          font-weight: bold;
          color: #000;
        }
        
        .qr-code-container {
          flex-shrink: 0;
          width: 12mm;
          height: 12mm;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        canvas {
          width: 12mm !important;
          height: 12mm !important;
          border: 1px solid #000;
          display: block;
        }
        
        .production-table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 2mm;
        }
        
        .production-table th,
        .production-table td {
          border: 1px solid #000;
          padding: 0.5mm;
          text-align: center;
          vertical-align: top;
          line-height: 1.0;
          font-size: 5px;
        }
        
        .production-table th {
          background-color: #f0f0f0;
          font-weight: bold;
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }
        
        .project-cell,
        .craft-cell {
          text-align: left;
          font-size: 4px;
        }
        
        @media print {
          body {
            margin: 0;
            padding: 3mm;
          }
        }
      </style>
    </head>
    <body>
      ${content}
      <div style="margin-top: 10mm; text-align: center; font-size: 10px; color: #666;">
        使用浏览器的打印功能（Ctrl+P 或 Cmd+P）进行打印
      </div>
    </body>
    </html>
  `

  // 写入内容
  printWindow.document.write(htmlContent)
  printWindow.document.close()

  // 等待加载完成
  printWindow.onload = function() {
    // 可以在这里添加二维码生成等逻辑
  }
}

/**
 * 检查打印支持
 */
export function checkPrintSupport() {
  return {
    newWindow: true,
    windowPrint: typeof window.print === 'function'
  }
}
