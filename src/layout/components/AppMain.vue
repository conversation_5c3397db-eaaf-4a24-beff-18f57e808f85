<template>
  <section class="app-main">
    <!-- 使用 el-watermark 组件 -->
    <!-- <el-watermark
        v-if="showWatermark" :content="watermarkText"
        :rotate="-10"
        :opacity="0.5"
        :font="font"
        :z-index="9999"
        > -->
      <router-view v-slot="{ Component, route }">
        <transition name="fade-transform" mode="out-in">
          <keep-alive :include="tagsViewStore.cachedViews">
            <component v-if="!route.meta.link" :is="Component" :key="route.path"/>
          </keep-alive>
        </transition>
      </router-view>
      <iframe-toggle/>
    <!-- </el-watermark> -->
  </section>
</template>

<script setup>
import {ref, onMounted, watch, reactive} from 'vue'
import iframeToggle from "./IframeToggle/index"
import useTagsViewStore from '@/store/modules/tagsView'
import {ElWatermark} from 'element-plus'

const route = useRoute()
const tagsViewStore = useTagsViewStore()

// 水印控制变量
const showWatermark = ref(true) // 控制水印显示
const watermarkText = ref(['MIS', '展会环境']) // 水印文字内容
const font = reactive({
  color: 'rgba(0,0,0,0.15)',
  fontSize: 16,
  fontFamily: 'sans-serif',
  fontStyle: 'normal',
  fontWeight: 'normal',
  textAlign: 'center',
  textBaseline: 'hanging'
})

onMounted(() => {
  addIframe()
})

watch(route, () => {
  addIframe()
})

function addIframe() {
  if (route.meta.link) {
    useTagsViewStore().addIframeView(route)
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>

