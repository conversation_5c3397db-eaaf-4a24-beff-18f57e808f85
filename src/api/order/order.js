import request from '@/utils/request'

// 查询客户订单列表
export function listOrder(query) {
  return request({
    url: '/order/order/list',
    method: 'get',
    params: query
  })
}

// 查询客户订单详细
export function getOrder(uuid) {
  return request({
    url: '/order/order/' + uuid,
    method: 'get'
  })
}

// 新增客户订单
export function addOrder(data) {
  return request({
    url: '/order/order',
    method: 'post',
    data: data
  })
}

// 修改更新印前状态
export function updatePrePressStatusByUuid(data) {
  return request({
    url: '/order/order/updatePrePressStatusByUuid',
    method: 'put',
    data: data
  })
}

// 付款状态处理
export function updatePayStatus(data) {
  return request({
    url: '/order/order/updatePayStatus',
    method: 'put',
    data: data
  })
}

// 修改客户订单
export function updateOrder(data) {
  return request({
    url: '/order/order',
    method: 'put',
    data: data
  })
}

// 删除客户订单
export function delOrder(uuid) {
  return request({
    url: '/order/order/' + uuid,
    method: 'delete'
  })
}

//获取拼板方案
export function getLayouts(data) {
  return request({
    url: '/order/order/getLayouts',
    method: 'post',
    data: data
  })
}

// 发送到打印机
export function confirmLayouts(data) {
  return request({
    url: '/order/order/confirmLayouts',
    method: 'post',
    data: data
  })
}

// 确认打印
export function manualTask(data) {
  return request({
    url: '/order/order/manualTask',
    method: 'post',
    data: data
  })
}

// 打印订单信息
export function orderFlowExport(uuid) {
  return request({
    url: '/order/order/orderFlowExport/' + uuid,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取工单打印数据（JSON格式）
export function getWorkOrderPrintData(uuid) {
  return request({
    url: '/order/order/getWorkOrderPrintData/' + uuid,
    method: 'get'
  })
}




  // 待发货订单-分页
  export function unshippedOrderListPage(query) {
    return request({
      url: '/order/order/unshippedOrderListPage',
      method: 'get',
      params: query
    })
  }

  // 打印结算单
export function orderSettlement(uuid) {
  return request({
    url: '/order/order/orderSettlement/' + uuid,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取共享文件夹目录树
export function getDirectoryTree() {
  return request({
    url: '/system/sharedFolder/tree',
    method: 'get'
  })
}
// 手动扫描刷新共享树
export function triggerScan() {
  return request({
    url: '/system/sharedFolder/scan',
    method: 'post'
  })
}
// 获取文件上传类型（共享盘、七牛云）
export function getBusinessUploadType() {
  return request({
    url: '/system/sharedFolder/getBusinessUploadType',
    method: 'get'
  })
}
