
## 平台简介


## 前端运行


# 安装依赖
yarn --registry=https://registry.npmmirror.com


# 展厅环境uat环境
yarn build:prod


# 上海印畅
yarn build:shyc → 生成 dist-shyc 文件夹
# 新湘彩
yarn build:shxc → 生成 dist-shxc 文件夹
# 展厅环境
yarn build:prod → 生成 distDemo 文件夹

# 构建测试环境 yarn build:stage
# 构建生产环境 yarn build:prod
# 前端访问地址 http://localhost:8089

rm -rf .history node_modules/.vite node_modules yarn.lock 
yarn install --force
yarn install
# 启动服务
yarn dev

