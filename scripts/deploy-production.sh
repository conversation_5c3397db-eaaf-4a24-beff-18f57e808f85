#!/bin/bash

# 生产环境部署脚本
# 包含性能优化的构建和部署流程

echo "🚀 开始生产环境部署..."

# 检查 Node.js 版本
NODE_VERSION=$(node -v)
echo "Node.js 版本: $NODE_VERSION"

# 检查内存
MEMORY=$(free -h | awk '/^Mem:/ {print $2}')
echo "系统内存: $MEMORY"

# 清理之前的构建
echo "🧹 清理之前的构建文件..."
rm -rf dist/
rm -rf node_modules/.cache/

# 安装依赖
echo "📦 安装依赖..."
npm ci --production=false

# 运行构建前检查
echo "🔍 运行构建前检查..."

# 检查关键文件是否存在

if [ ! -f ".env.production" ]; then
    echo "❌ 缺少生产环境配置文件"
    exit 1
fi

# 运行 ESLint 检查（如果存在）
if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
    echo "🔍 运行代码检查..."
    npm run lint --if-present
fi

# 设置构建环境变量
export NODE_ENV=production
export VITE_APP_ENV=production

# 开始构建
echo "🏗️ 开始生产环境构建..."
npm run build:prod

# 检查构建结果
if [ ! -d "dist" ]; then
    echo "❌ 构建失败，dist 目录不存在"
    exit 1
fi

# 检查构建文件大小
echo "📊 检查构建文件大小..."
DIST_SIZE=$(du -sh dist/ | cut -f1)
echo "构建文件总大小: $DIST_SIZE"

# 检查 JS 文件大小
JS_FILES=$(find dist/assets -name "*.js" -type f)
for file in $JS_FILES; do
    SIZE=$(du -h "$file" | cut -f1)
    echo "JS文件: $(basename $file) - $SIZE"

    # 警告大文件
    SIZE_BYTES=$(du -b "$file" | cut -f1)
    if [ $SIZE_BYTES -gt 1048576 ]; then  # 1MB
        echo "⚠️  警告: $file 文件过大 ($SIZE)"
    fi
done

# 检查 CSS 文件大小
CSS_FILES=$(find dist/assets -name "*.css" -type f)
for file in $CSS_FILES; do
    SIZE=$(du -h "$file" | cut -f1)
    echo "CSS文件: $(basename $file) - $SIZE"
done

# 生成构建报告
echo "📋 生成构建报告..."
cat > dist/build-report.txt << EOF
构建时间: $(date)
Node.js版本: $NODE_VERSION
构建大小: $DIST_SIZE
构建模式: production
性能优化: 已启用
内存优化: 已启用
代码压缩: 已启用
Console日志: 已移除
EOF

# 运行构建后测试（如果存在）
if [ -f "scripts/post-build-test.js" ]; then
    echo "🧪 运行构建后测试..."
    node scripts/post-build-test.js
fi

echo "✅ 生产环境构建完成！"
echo "📁 构建文件位于: ./dist/"
echo "📊 构建大小: $DIST_SIZE"

# 提供部署建议
echo ""
echo "🚀 部署建议:"
echo "1. 确保服务器支持 gzip 压缩"
echo "2. 设置适当的缓存策略"
echo "3. 监控应用性能和内存使用"
echo "4. 定期检查错误日志"

# 如果有部署配置，执行部署
if [ -f "deploy.config.js" ]; then
    echo "🚀 开始部署到服务器..."
    # 这里可以添加具体的部署逻辑
    # 例如: rsync, scp, 或调用 CI/CD 工具
fi

echo "🎉 部署流程完成！"
