{"name": "mis-web", "version": "1.0.0", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "build:bjjm": "rm -f public/favicon.ico && cp client-assets/bjjm/logo/logo.png src/assets/logo/logo.png && vite build --mode bjjm && cp client-assets/default/favicon.ico public/favicon.ico && cp client-assets/default/logo.png src/assets/logo/logo.png", "build:shyc": "rm -f public/favicon.ico && cp client-assets/shyc/logo/logo.png src/assets/logo/logo.png && vite build --mode shyc && cp client-assets/default/favicon.ico public/favicon.ico && cp client-assets/default/logo.png src/assets/logo/logo.png", "build:shxc": "rm -f public/favicon.ico && cp client-assets/shxc/logo/logo.png src/assets/logo/logo.png && vite build --mode shxc && cp client-assets/default/favicon.ico public/favicon.ico && cp client-assets/default/logo.png src/assets/logo/logo.png", "preview": "vite preview"}, "repository": {"type": "git", "url": ""}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.5", "@element-plus/icons-vue": "^2.3.1", "@lezer/highlight": "^1.2.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "clipboard": "2.0.11", "codemirror": "^6.0.1", "echarts": "5.5.1", "element-plus": "2.9.4", "file-saver": "2.0.5", "formula-parser": "^2.0.1", "fuse.js": "6.6.2", "glob": "^10.3.10", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "mathjs": "^14.4.0", "moment": "^2.30.1", "nprogress": "0.2.0", "pdfjs-dist": "^3.11.174", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^4.4.1", "qiniu-js": "3.4.2", "qrcode": "^1.5.4", "sortablejs": "^1.15.6", "splitpanes": "3.1.5", "vue": "3.3.4", "vue-cropper": "1.1.1", "vue-pdf": "^4.3.0", "vue-router": "4.4.0", "vuedraggable": "4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "terser": "^5.43.1", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}